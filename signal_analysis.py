import pandas as pd
import numpy as np
from datetime import datetime

# Load the processed data
def analyze_signals():
    try:
        # Load the data file
        df = pd.read_csv('HIFIUSDT_Historical_Data.csv', index_col='TIME', parse_dates=True)
        print(f"Loaded {len(df)} records for HIFIUSDT")
        
        # Check for the main trading signals
        main_signals = ['Buy_S_P_001', 'Sell_S_P_001', 'Buy_S_P_004', 'Sell_S_P_004', 
                       'Buy_S_P_007', 'Sell_S_P_007', 'Buy_S_P_012', 'Sell_S_P_012',
                       'Buy_S_P_015', 'Sell_S_P_015', 'Buy_S_P_017', 'Sell_S_P_017',
                       'Buy_S_P_018', 'Sell_S_P_018']
        
        print("\n=== SIGNAL ANALYSIS FOR HIFIUSDT ===")
        
        # Count signals for each layer
        signal_counts = {}
        for signal in main_signals:
            if signal in df.columns:
                count = df[signal].notna().sum()
                signal_counts[signal] = count
                if count > 0:
                    print(f"{signal}: {count} signals")
                    # Show the actual signal values
                    signals = df[df[signal].notna()][signal]
                    if len(signals) > 0:
                        print(f"  Latest signals: {signals.tail(3).tolist()}")
        
        # Check for missing signals by analyzing market conditions
        print("\n=== MARKET CONDITION ANALYSIS ===")
        
        # Check recent price action
        recent_data = df.tail(100)  # Last 100 periods
        
        # Price movement analysis
        price_change = ((recent_data['CLOSE'].iloc[-1] - recent_data['CLOSE'].iloc[0]) / recent_data['CLOSE'].iloc[0]) * 100
        print(f"Recent price change (last 100 periods): {price_change:.2f}%")
        
        # Volatility analysis
        recent_volatility = recent_data['CLOSE'].pct_change().std() * 100
        print(f"Recent volatility: {recent_volatility:.2f}%")
        
        # Moving average analysis
        if 'P_S_1' in df.columns and 'P_S_2' in df.columns:
            ma_cross_recent = recent_data['P_S_1'] > recent_data['P_S_2']
            current_trend = "Bullish" if ma_cross_recent.iloc[-1] else "Bearish"
            trend_changes = (ma_cross_recent != ma_cross_recent.shift()).sum()
            print(f"Current trend: {current_trend}")
            print(f"Trend changes in last 100 periods: {trend_changes}")
        
        # RSI analysis
        if 'RSI' in df.columns:
            current_rsi = recent_data['RSI'].iloc[-1]
            rsi_oversold = (recent_data['RSI'] < 30).sum()
            rsi_overbought = (recent_data['RSI'] > 70).sum()
            print(f"Current RSI: {current_rsi:.2f}")
            print(f"RSI oversold periods: {rsi_oversold}")
            print(f"RSI overbought periods: {rsi_overbought}")
        
        # Check for potential missed signals
        print("\n=== POTENTIAL MISSED SIGNALS ANALYSIS ===")
        
        # Look for strong price movements without signals
        strong_moves = recent_data[abs(recent_data['CLOSE'].pct_change()) > 0.02]  # >2% moves
        print(f"Strong price movements (>2%): {len(strong_moves)}")
        
        if len(strong_moves) > 0:
            print("Dates with strong moves:")
            for idx, row in strong_moves.tail(5).iterrows():
                change = row['CLOSE'] / recent_data['CLOSE'].shift(1).loc[idx] - 1
                print(f"  {idx}: {change*100:.2f}% move")
        
        # Check signal timing
        print("\n=== SIGNAL TIMING ANALYSIS ===")
        
        # Find all signal dates
        all_signal_dates = set()
        for signal in main_signals:
            if signal in df.columns:
                signal_dates = df[df[signal].notna()].index
                all_signal_dates.update(signal_dates)
        
        if all_signal_dates:
            sorted_dates = sorted(all_signal_dates)
            print(f"Total unique signal dates: {len(sorted_dates)}")
            if len(sorted_dates) > 0:
                print(f"First signal: {sorted_dates[0]}")
                print(f"Last signal: {sorted_dates[-1]}")
                
                # Check signal frequency
                if len(sorted_dates) > 1:
                    time_diffs = [(sorted_dates[i] - sorted_dates[i-1]).total_seconds() / 3600 
                                 for i in range(1, len(sorted_dates))]
                    avg_hours_between = np.mean(time_diffs)
                    print(f"Average hours between signals: {avg_hours_between:.2f}")
        
        # Check for recent signal drought
        if all_signal_dates:
            last_signal_date = max(all_signal_dates)
            hours_since_last = (df.index[-1] - last_signal_date).total_seconds() / 3600
            print(f"Hours since last signal: {hours_since_last:.2f}")
            
            if hours_since_last > 24:  # More than 24 hours
                print("⚠️  WARNING: No signals in last 24 hours - potential missed opportunities")
                
                # Analyze why no recent signals
                recent_no_signals = df[df.index > last_signal_date].tail(20)
                if len(recent_no_signals) > 0:
                    print("\nRecent market conditions (no signals):")
                    print(f"  Price range: {recent_no_signals['CLOSE'].min():.4f} - {recent_no_signals['CLOSE'].max():.4f}")
                    if 'RSI' in recent_no_signals.columns:
                        print(f"  RSI range: {recent_no_signals['RSI'].min():.2f} - {recent_no_signals['RSI'].max():.2f}")
                    if 'P_S_1' in recent_no_signals.columns and 'P_S_2' in recent_no_signals.columns:
                        ma_separation = abs(recent_no_signals['P_S_1'] - recent_no_signals['P_S_2']) / recent_no_signals['P_S_2'] * 100
                        print(f"  MA separation: {ma_separation.mean():.3f}% avg")
        
        return df, signal_counts
        
    except Exception as e:
        print(f"Error in signal analysis: {e}")
        return None, {}

if __name__ == "__main__":
    df, signals = analyze_signals()
