from binance.client import Client
import pandas as pd
import matplotlib.pyplot as plt
import ta
from datetime import datetime
import numpy as np
import os
import yagmail
import time
import pytz
import logging
from scipy.signal import argrelextrema

import warnings
#warnings.simplefilter(action='ignore', category=FutureWarning)

# IMPROVED TRADING SIGNAL SYSTEM
# This module implements an enhanced trading signal system with multiple filters to reduce false signals:
#
# IMPROVEMENTS MADE:
# 1. Signal Confirmation: Requires multiple consecutive confirmations before generating signals
# 2. RSI Filter: Avoids buying when RSI is overbought (>70) and selling when oversold (<30)
# 3. MACD Filter: Uses MACD crossover as additional confirmation
# 4. Trend Strength Filter: Uses ADX to ensure signals occur during strong trends
# 5. Price Separation Filter: Ensures sufficient separation between moving averages
# 6. Momentum Filter: Considers recent price momentum
# 7. Configurable Parameters: Easy to adjust thresholds and enable/disable filters
# 8. Enhanced Visualization: Added MACD and RSI charts for better analysis
# 9. Signal Statistics: Provides summary of signal performance

#time.sleep(4) # Delay for 5 seconds.
#print(plt.style.available)
plt.style.use("seaborn-v0_8-whitegrid")

# Record the start time
start_time = time.time()

current_time = datetime.now()
formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')

# Binance API credentials
api_key = "RD26JGzkICKfBQfs8NcWR1hAgpcjkVNi0YwqRC36vSQM0kZx1uwn9n3k0tePpSPD"
api_secret = "D4RQXB2zeCc2QgqSvmIM24MTqVApKTIU8XLXKuN5qdHkf8gwI3Aqx5RcvCyWrLVj"
binance_client = Client(api_key, api_secret)

Coin = 'BAKEUSDT'
ID  =  '1_0'
TF = '3m'
ACC = 'Hadi'

# Strategy Configuration Parameters - Balanced for Accuracy and Opportunity
SIGNAL_CONFIRMATION_REQUIRED = 2  # Balanced confirmations
MIN_TREND_STRENGTH = 15  # Lower ADX for more opportunities in moderate trends
RSI_OVERSOLD_THRESHOLD = 30  # Standard RSI levels
RSI_OVERBOUGHT_THRESHOLD = 70
MIN_PRICE_SEPARATION_PCT = 0.001  # Lower separation requirement
MIN_VOLUME_RATIO = 0.8  # More lenient volume requirement
LOOKBACK_PERIODS = 3  # Shorter lookback for faster response
ENABLE_TREND_FILTER = True
ENABLE_RSI_FILTER = True
ENABLE_MACD_FILTER = True
ENABLE_VOLUME_FILTER = False  # Disable volume filter for now
ENABLE_STRICT_MODE = False  # Toggle for strict vs balanced mode

# Data file
data_file_name = f"{Coin}_Historical_Data.csv"

# ------------------------------
# Retry Wrapper
# ------------------------------
def with_retries(func, max_retries=3, delay=2, **kwargs):
    """Retry wrapper for API calls"""
    for attempt in range(max_retries):
        try:
            return func(**kwargs)
        except Exception as e:
            print(f"{formatted_time} - Attempt {attempt+1}/{max_retries} failed: {e}")
            time.sleep(delay)
    print(f"{formatted_time} - ERROR: All retries failed for {func.__name__}")
    return None

# ------------------------------
# Load & Save
# ------------------------------
def load_existing_data():
    if os.path.exists(data_file_name):
        try:
            df = pd.read_csv(data_file_name, index_col='TIME', parse_dates=True)
            return df
        except Exception as e:
            print(f"{formatted_time} - Error loading existing data: {e}")
            return pd.DataFrame()
    return pd.DataFrame()

def save_data(df):
    try:
        df.to_csv(data_file_name, index_label='TIME')  # Ensure TIME index column
        print(f"{formatted_time} - Downloading Completed")
        return True
    except PermissionError:
        print(f"{formatted_time} - Permission denied. Close programs using the file.")
        return False
    except Exception as e:
        print(f"{formatted_time} - Error saving: {e}")
        return False

# ------------------------------
# Fetching
# ------------------------------
def fetch_klines(symbol, start_time=None, end_time=None, limit=1500):
    """Wrapper around futures_klines with retry logic"""
    try:
        params = {"symbol": symbol, "interval": TF, "limit": limit}
        if start_time:
            params["startTime"] = int(start_time.timestamp() * 1000)
        if end_time:
            params["endTime"] = int(end_time.timestamp() * 1000)

        raw = with_retries(binance_client.futures_klines, **params)
        if not raw:
            return pd.DataFrame()

        df = pd.DataFrame(raw).iloc[:, :6]
        df.columns = ['TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOLUME']
        df['TIME'] = pd.to_datetime(df['TIME'], unit='ms')
        df.set_index('TIME', inplace=True)

        for col in ['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOLUME']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df = df.dropna()

        return df
    except Exception as e:
        print(f"{formatted_time} - Error fetching data: {e}")
        return pd.DataFrame()

def fetch_complete_data(symbol, days_back=10):
    """Fetch complete data by looping in chunks"""
    all_data = []
    now = datetime.now()
    target_start = now - pd.Timedelta(days=days_back)
    total_needed = days_back * 24 * 60 // 3

    current_start = target_start
    while current_start < now and len(all_data) < total_needed:
        df = fetch_klines(symbol, start_time=current_start, limit=1500)
        if df.empty:
            break

        df = df[df.index >= target_start]
        all_data.append(df)
        current_start = df.index.max() + pd.Timedelta(minutes=3)
        time.sleep(0.1)

    if all_data:
        combined = pd.concat(all_data)
        combined = combined[~combined.index.duplicated(keep='last')]
        combined = combined.sort_index()
        combined = combined[combined.index >= target_start]
        return combined
    else:
        print(f"{formatted_time} - ERROR: Failed to fetch any data")
        return pd.DataFrame()

def fetch_latest_data(symbol):
    return fetch_klines(symbol, limit=1500)

# ------------------------------
# Validation & Cleanup
# ------------------------------
def validate_and_report_completeness(df):
    if len(df) < 2:
        print(f"{formatted_time} - ERROR: Insufficient data for validation")
        return False

    data_span = (df.index.max() - df.index.min()).total_seconds() / (24 * 3600)
    expected_points = int((data_span * 24 * 60) // 3)
    actual_points = len(df)

    time_diffs = df.index.to_series().diff().dropna()
    expected_interval = pd.Timedelta(minutes=3)
    perfect_intervals = (time_diffs == expected_interval).sum()
    large_gaps = (time_diffs > pd.Timedelta(minutes=6)).sum()

    total_intervals = len(time_diffs)
    interval_completeness = (perfect_intervals / total_intervals) * 100 if total_intervals > 0 else 0
    span_coverage = (actual_points / expected_points) * 100 if expected_points > 0 else 0

    is_complete = span_coverage >= 90 and interval_completeness >= 90 and large_gaps == 0

    if not is_complete and span_coverage < 80:
        print(f"{formatted_time} - ERROR: Data has significant gaps")

    return is_complete

def final_cleanup(df):
    df = df[~df.index.duplicated(keep='last')]
    df = df.sort_index()

    for col in ['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOLUME']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # Fix future warning for fillna
    df.ffill(inplace=True)
    df.bfill(inplace=True)

    df = df.dropna()
    is_complete = validate_and_report_completeness(df)
    return df, is_complete

# ------------------------------
# Data Management
# ------------------------------
def build_incremental_dataset():
    now = datetime.now()
    ten_days_ago = now - pd.Timedelta(days=10)

    existing_data = load_existing_data()
    print(f"{formatted_time} - Downloading Latest Data...")
    latest_data = fetch_latest_data(Coin)

    if latest_data.empty:
        print(f"{formatted_time} - ERROR: Failed to fetch latest data")
        return existing_data

    if not existing_data.empty:
        combined = pd.concat([existing_data, latest_data])
    else:
        combined = latest_data

    combined = combined[~combined.index.duplicated(keep='last')]
    combined = combined.sort_index()
    combined = combined[combined.index >= ten_days_ago]
    return combined

def smart_data_management():
    now = datetime.now()
    ten_days_ago = now - pd.Timedelta(days=10)

    existing_data = load_existing_data()
    if not existing_data.empty:
        existing_data = existing_data[existing_data.index >= ten_days_ago]

    expected_records = (10 * 24 * 60) // 3
    current_coverage = len(existing_data) / expected_records * 100 if not existing_data.empty else 0

    if current_coverage < 80:
        combined = build_incremental_dataset()
    else:
        latest_data = fetch_latest_data(Coin)
        if not latest_data.empty:
            combined = pd.concat([existing_data, latest_data])
            combined = combined[~combined.index.duplicated(keep='last')]
            combined = combined.sort_index()
            combined = combined[combined.index >= ten_days_ago]
        else:
            combined = existing_data

    if not combined.empty:
        combined, is_complete = final_cleanup(combined)
        if save_data(combined):
            print(f"{formatted_time} - Data Saved to File: {data_file_name}")
        return combined, is_complete
    else:
        print(f"{formatted_time} - ERROR: Failed to build dataset")
        return pd.DataFrame(), False

# ------------------------------
# Processed Data Loader
# ------------------------------
def get_processed_data():
    if not os.path.exists(data_file_name):
        print(f"{formatted_time} - ERROR: Data file {data_file_name} not found. Run script first.")
        return pd.DataFrame()

    df = pd.read_csv(data_file_name, index_col='TIME', parse_dates=True)
    ten_days_ago = datetime.now() - pd.Timedelta(days=10)
    df = df[df.index >= ten_days_ago]
    df, _ = final_cleanup(df)
    return df

def load_and_process_data():
    return get_processed_data()

# ------------------------------
# Main
# ------------------------------
if __name__ == "__main__":
    print(f"{formatted_time} - Data Process Started For {Coin}...")
    test_001, is_complete = smart_data_management()

    if not test_001.empty:
        if is_complete:
            print(f"{formatted_time} - Data Process Completed: {len(test_001)} records")
        else:
            print(f"{formatted_time} - WARNING: Data downloaded with gaps: {len(test_001)} records")
    else:
        print(f"{formatted_time} - ERROR: Data download failed")
else:
    test_001 = get_processed_data()

#print(f"{formatted_time} - INFO: Data analytics process initiated")
print(f"{formatted_time} - INFO: Data analytics process initiated ░░░░░░░░░░ 0%")

# First level smoothing with increased span to reduce noise
test_001['P_S_00'] = test_001['OPEN'].ewm(span=4, adjust=False).mean()
test_001['P_S_01'] = test_001['CLOSE'].ewm(span=4, adjust=False).mean()
test_001['P_S_02'] = (test_001['P_S_00'] + test_001['P_S_01'])/2

# Hierarchical smoothing with optimized spans to minimize false crossovers
test_001['P_S_0'] = 0.6 * test_001['P_S_02'].ewm(span=45, adjust=False).mean() + \
                    0.25 * test_001['P_S_02'].ewm(span=45, adjust=False).mean().ewm(span=22, adjust=False).mean() + \
                    0.15 * test_001['P_S_02'].ewm(span=45, adjust=False).mean().ewm(span=22, adjust=False).mean().ewm(span=11, adjust=False).mean()

test_001['P_S_1'] = 0.5 * test_001['P_S_02'].ewm(span=75, adjust=False).mean() + \
                    0.3 * test_001['P_S_02'].ewm(span=75, adjust=False).mean().ewm(span=37, adjust=False).mean() + \
                    0.2 * test_001['P_S_02'].ewm(span=75, adjust=False).mean().ewm(span=37, adjust=False).mean().ewm(span=18, adjust=False).mean()

test_001['P_S_2'] = 0.4 * test_001['P_S_02'].ewm(span=95, adjust=False).mean() + \
                    0.35 * test_001['P_S_02'].ewm(span=95, adjust=False).mean().ewm(span=47, adjust=False).mean() + \
                    0.25 * test_001['P_S_02'].ewm(span=95, adjust=False).mean().ewm(span=47, adjust=False).mean().ewm(span=23, adjust=False).mean()

test_001['P_S_3'] = 0.35 * test_001['P_S_02'].ewm(span=60, adjust=False).mean() + \
                    0.4 * test_001['P_S_02'].ewm(span=60, adjust=False).mean().ewm(span=30, adjust=False).mean() + \
                    0.25 * test_001['P_S_02'].ewm(span=60, adjust=False).mean().ewm(span=30, adjust=False).mean().ewm(span=15, adjust=False).mean()

# Add crossover confirmation to reduce false signals
test_001['P_S_1_smooth'] = test_001['P_S_1'].ewm(span=3, adjust=False).mean()
test_001['P_S_2_smooth'] = test_001['P_S_2'].ewm(span=3, adjust=False).mean()

# Calculate separation percentage to filter out minor crossovers
test_001['MA_separation'] = abs(test_001['P_S_1'] - test_001['P_S_2']) / test_001['P_S_2'] * 100

# Add crossover confirmation system
def add_crossover_confirmation(df, fast_col='P_S_1', slow_col='P_S_2', confirmation_periods=3, min_separation=0.02):
    """
    Add confirmed crossover signals that require sustained crossover for multiple periods
    and minimum separation to avoid false signals
    """
    df['crossover_raw'] = 0
    df['crossover_confirmed'] = 0

    # Detect raw crossovers
    for i in range(1, len(df)):
        if df[fast_col].iloc[i] > df[slow_col].iloc[i] and df[fast_col].iloc[i-1] <= df[slow_col].iloc[i-1]:
            df.loc[df.index[i], 'crossover_raw'] = 1  # Bullish crossover
        elif df[fast_col].iloc[i] < df[slow_col].iloc[i] and df[fast_col].iloc[i-1] >= df[slow_col].iloc[i-1]:
            df.loc[df.index[i], 'crossover_raw'] = -1  # Bearish crossover

    # Confirm crossovers that are sustained and have minimum separation
    for i in range(confirmation_periods, len(df)):
        if df['crossover_raw'].iloc[i] == 1:  # Potential bullish crossover
            # Check if crossover is sustained for confirmation_periods
            sustained = all(df[fast_col].iloc[i-j] > df[slow_col].iloc[i-j] for j in range(confirmation_periods))
            # Check minimum separation
            current_separation = df['MA_separation'].iloc[i]
            if sustained and current_separation >= min_separation:
                df.loc[df.index[i], 'crossover_confirmed'] = 1

        elif df['crossover_raw'].iloc[i] == -1:  # Potential bearish crossover
            # Check if crossover is sustained for confirmation_periods
            sustained = all(df[fast_col].iloc[i-j] < df[slow_col].iloc[i-j] for j in range(confirmation_periods))
            # Check minimum separation
            current_separation = df['MA_separation'].iloc[i]
            if sustained and current_separation >= min_separation:
                df.loc[df.index[i], 'crossover_confirmed'] = -1

    return df

# Apply crossover confirmation
test_001 = add_crossover_confirmation(test_001, 'P_S_1', 'P_S_2', confirmation_periods=3, min_separation=0.03)


# Add additional indicators for signal filtering
test_001['RSI'] = ta.momentum.RSIIndicator(test_001['CLOSE'], window=14).rsi()
test_001['MACD'] = ta.trend.MACD(test_001['CLOSE']).macd()
test_001['MACD_signal'] = ta.trend.MACD(test_001['CLOSE']).macd_signal()
test_001['MACD_histogram'] = ta.trend.MACD(test_001['CLOSE']).macd_diff()

# Calculate trend strength using ADX
test_001['ADX'] = ta.trend.ADXIndicator(test_001['HIGH'], test_001['LOW'], test_001['CLOSE']).adx()
test_001['+DI'] = ta.trend.ADXIndicator(test_001['HIGH'], test_001['LOW'], test_001['CLOSE']).adx_pos()
test_001['-DI'] = ta.trend.ADXIndicator(test_001['HIGH'], test_001['LOW'], test_001['CLOSE']).adx_neg()

# Calculate volatility using ATR
test_001['ATR'] = ta.volatility.AverageTrueRange(test_001['HIGH'], test_001['LOW'], test_001['CLOSE']).average_true_range()

# Calculate percentage change for momentum
test_001['Price_Change_Pct'] = test_001['CLOSE'].pct_change() * 100

# Calculate volume indicators
test_001['Volume_SMA'] = test_001['VOLUME'].rolling(window=20).mean()
test_001['Volume_Ratio'] = test_001['VOLUME'] / test_001['Volume_SMA']

# Calculate moving average slopes for trend direction
test_001['P_S_1_Slope'] = test_001['P_S_1'].diff(LOOKBACK_PERIODS)
test_001['P_S_2_Slope'] = test_001['P_S_2'].diff(LOOKBACK_PERIODS)

# Calculate price position relative to moving averages
test_001['Price_Above_P_S_1'] = test_001['CLOSE'] > test_001['P_S_1']
test_001['Price_Above_P_S_2'] = test_001['CLOSE'] > test_001['P_S_2']

# Calculate recent highs and lows for breakout detection
test_001['Recent_High'] = test_001['HIGH'].rolling(window=10).max()
test_001['Recent_Low'] = test_001['LOW'].rolling(window=10).min()
test_001['Near_High'] = test_001['CLOSE'] > (test_001['Recent_High'] * 0.995)
test_001['Near_Low'] = test_001['CLOSE'] < (test_001['Recent_Low'] * 1.005)

# Initialize lists to store buy/sell signals and flags
BUY_001 = []
SELL_001 = []
b_list_001 = []
s_list_001 = []

# Initialize position flag and signal confirmation counters
position_001 = False
buy_confirmation_count = 0
sell_confirmation_count = 0

# Use configuration parameters
required_confirmation = SIGNAL_CONFIRMATION_REQUIRED
min_trend_strength = MIN_TREND_STRENGTH
rsi_oversold = RSI_OVERSOLD_THRESHOLD
rsi_overbought = RSI_OVERBOUGHT_THRESHOLD
min_price_separation = MIN_PRICE_SEPARATION_PCT
min_volume_ratio = MIN_VOLUME_RATIO
lookback_periods = LOOKBACK_PERIODS

# Iterate over the DataFrame rows
for idx_001, row in test_001.iterrows():
    # Get the integer position of the current index
    i = test_001.index.get_loc(idx_001)

    # Skip first few rows to ensure indicators are properly calculated
    if i < 30:  # Increased to allow more indicators to stabilize
        BUY_001.append(np.nan)
        b_list_001.append(0)
        SELL_001.append(np.nan)
        s_list_001.append(0)
        continue

    # Calculate percentage separation between moving averages
    price_separation = abs(row['P_S_1'] - row['P_S_2']) / row['CLOSE'] * 100

    # Enhanced trend and market condition analysis
    trend_strong = (row['ADX'] > min_trend_strength if not pd.isna(row['ADX']) else False) if ENABLE_TREND_FILTER else True

    # More sophisticated MACD analysis
    macd_bullish = False
    macd_bearish = False
    if ENABLE_MACD_FILTER and not pd.isna(row['MACD']) and not pd.isna(row['MACD_signal']):
        macd_bullish = (row['MACD'] > row['MACD_signal'] and row['MACD_histogram'] > 0)
        macd_bearish = (row['MACD'] < row['MACD_signal'] and row['MACD_histogram'] < 0)
    elif not ENABLE_MACD_FILTER:
        macd_bullish = True
        macd_bearish = True

    # Directional movement analysis
    di_bullish = (row['+DI'] > row['-DI'] if not pd.isna(row['+DI']) and not pd.isna(row['-DI']) else True)
    di_bearish = (row['-DI'] > row['+DI'] if not pd.isna(row['+DI']) and not pd.isna(row['-DI']) else True)

    # Volume confirmation
    volume_confirmed = (row['Volume_Ratio'] > min_volume_ratio if not pd.isna(row['Volume_Ratio']) else True) if ENABLE_VOLUME_FILTER else True

    # Moving average slope analysis
    ma_trending_up = (row['P_S_1_Slope'] > 0 and row['P_S_2_Slope'] > 0 if not pd.isna(row['P_S_1_Slope']) and not pd.isna(row['P_S_2_Slope']) else True)
    ma_trending_down = (row['P_S_1_Slope'] < 0 and row['P_S_2_Slope'] < 0 if not pd.isna(row['P_S_1_Slope']) and not pd.isna(row['P_S_2_Slope']) else True)

    if not position_001:
        # Enhanced buy condition analysis
        basic_buy_condition = row['P_S_1'] > row['P_S_2']

        # Price position analysis
        price_above_mas = row['Price_Above_P_S_1'] and row['Price_Above_P_S_2']

        # RSI analysis - more nuanced approach
        rsi_favorable = True
        if ENABLE_RSI_FILTER and not pd.isna(row['RSI']):
            rsi_favorable = (row['RSI'] > 40 and row['RSI'] < rsi_overbought)  # Avoid extreme oversold and overbought

        # Enhanced momentum analysis
        momentum_positive = row['Price_Change_Pct'] > 0.1 if not pd.isna(row['Price_Change_Pct']) else False  # Require stronger momentum

        # Breakout detection
        breakout_potential = not row['Near_High'] if not pd.isna(row['Near_High']) else True  # Avoid buying at recent highs

        # Combine buy conditions based on mode
        if ENABLE_STRICT_MODE:
            # Strict mode - all conditions must be met
            strong_buy_signal = (basic_buy_condition and
                               price_above_mas and
                               rsi_favorable and
                               price_separation > min_price_separation and
                               volume_confirmed and
                               trend_strong and
                               di_bullish and
                               macd_bullish and
                               ma_trending_up and
                               momentum_positive and
                               breakout_potential)
        else:
            # Balanced mode - core conditions + some confirmations
            core_conditions = (basic_buy_condition and
                             rsi_favorable and
                             price_separation > min_price_separation)

            confirmation_score = 0
            if price_above_mas: confirmation_score += 1
            if volume_confirmed: confirmation_score += 1
            if trend_strong and di_bullish: confirmation_score += 2
            if macd_bullish: confirmation_score += 1
            if ma_trending_up: confirmation_score += 1
            if momentum_positive: confirmation_score += 1
            if breakout_potential: confirmation_score += 1

            strong_buy_signal = core_conditions and confirmation_score >= 4

        if strong_buy_signal:
            buy_confirmation_count += 1
            if buy_confirmation_count >= required_confirmation:
                BUY_001.append(row['CLOSE'])
                b_list_001.append(row['CLOSE'])
                SELL_001.append(np.nan)
                s_list_001.append(0)
                position_001 = True
                buy_confirmation_count = 0  # Reset counter
                sell_confirmation_count = 0  # Reset sell counter
            else:
                BUY_001.append(np.nan)
                b_list_001.append(0)
                SELL_001.append(np.nan)
                s_list_001.append(0)
        else:
            buy_confirmation_count = 0  # Reset if condition not met
            BUY_001.append(np.nan)
            b_list_001.append(0)
            SELL_001.append(np.nan)
            s_list_001.append(0)
    else:
        # Enhanced sell condition analysis
        basic_sell_condition = row['P_S_1'] < row['P_S_2']

        # Price position analysis
        price_below_mas = not row['Price_Above_P_S_1'] and not row['Price_Above_P_S_2']

        # RSI analysis - more nuanced approach
        rsi_favorable = True
        if ENABLE_RSI_FILTER and not pd.isna(row['RSI']):
            rsi_favorable = (row['RSI'] < 60 and row['RSI'] > rsi_oversold)  # Avoid extreme oversold and overbought

        # Enhanced momentum analysis
        momentum_negative = row['Price_Change_Pct'] < -0.1 if not pd.isna(row['Price_Change_Pct']) else False  # Require stronger negative momentum

        # Breakdown detection
        breakdown_potential = not row['Near_Low'] if not pd.isna(row['Near_Low']) else True  # Avoid selling at recent lows

        # Combine sell conditions based on mode
        if ENABLE_STRICT_MODE:
            # Strict mode - all conditions must be met
            strong_sell_signal = (basic_sell_condition and
                                price_below_mas and
                                rsi_favorable and
                                price_separation > min_price_separation and
                                volume_confirmed and
                                trend_strong and
                                di_bearish and
                                macd_bearish and
                                ma_trending_down and
                                momentum_negative and
                                breakdown_potential)
        else:
            # Balanced mode - core conditions + some confirmations
            core_conditions = (basic_sell_condition and
                             rsi_favorable and
                             price_separation > min_price_separation)

            confirmation_score = 0
            if price_below_mas: confirmation_score += 1
            if volume_confirmed: confirmation_score += 1
            if trend_strong and di_bearish: confirmation_score += 2
            if macd_bearish: confirmation_score += 1
            if ma_trending_down: confirmation_score += 1
            if momentum_negative: confirmation_score += 1
            if breakdown_potential: confirmation_score += 1

            strong_sell_signal = core_conditions and confirmation_score >= 4

        if strong_sell_signal:
            sell_confirmation_count += 1
            if sell_confirmation_count >= required_confirmation:
                BUY_001.append(np.nan)
                b_list_001.append(0)
                SELL_001.append(row['CLOSE'])
                s_list_001.append(row['CLOSE'])
                position_001 = False
                sell_confirmation_count = 0  # Reset counter
                buy_confirmation_count = 0  # Reset buy counter
            else:
                BUY_001.append(np.nan)
                b_list_001.append(0)
                SELL_001.append(np.nan)
                s_list_001.append(0)
        else:
            sell_confirmation_count = 0  # Reset if condition not met
            BUY_001.append(np.nan)
            b_list_001.append(0)
            SELL_001.append(np.nan)
            s_list_001.append(0)

# Add the buy/sell signals and flags to the DataFrame
test_001['Buy_S_P_001'] = BUY_001
test_001['Sell_S_P_001'] = SELL_001
test_001['Buy_F_001'] = b_list_001
test_001['Sell_F_001'] = s_list_001


# Replace 0 with NaN in the 'Sell_F_001' column and assign it to the 'Signal_V_001' column
test_001['Signal_V_001'] = test_001['Sell_F_001'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_001' column with corresponding 'Buy_F_001' values by replacing 0 with NaN, and apply forward-fill
test_001['Signal_V_001'] = test_001['Signal_V_001'].fillna(test_001['Buy_F_001'].replace(0, np.nan)).ffill()

# Initialize 'Flag_001' column with NaN values and ensure it's of type 'object' (string)
test_001['Flag_001'] = np.nan  # Initialize with NaN
test_001['Flag_001'] = test_001['Flag_001'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_001 and 'S' for Sell_F_001 in the 'Flag_001' column
test_001.loc[test_001['Buy_F_001'] != 0, 'Flag_001'] = 'B'
test_001.loc[test_001['Sell_F_001'] != 0, 'Flag_001'] = 'S'

# Forward-fill the NaN values in the 'Flag_001' column
test_001['Flag_001'] = test_001['Flag_001'].ffill()

# Initialize 'P_PCT_001' column with 0
test_001['P_PCT_001'] = 0.0

# Signal PCT
# Initialize the P_PCT_001 column without raising warnings
test_001.loc[:, 'P_PCT_001'] = 0.0  # Initialize safely

for i in range(1, len(test_001)):
    # Check for the condition related to B_Flag_001
    if test_001.iloc[i, test_001.columns.get_loc('Flag_001')] == 'B' and test_001.iloc[i-1, test_001.columns.get_loc('Flag_001')] != 'B':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = 0
        
    # Check for the condition related to S_Flag_001
    elif test_001.iloc[i, test_001.columns.get_loc('Flag_001')] == 'S' and test_001.iloc[i-1, test_001.columns.get_loc('Flag_001')] != 'S':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = 0
        
    # Existing logic for calculating P_PCT_001 for B and S flags
    elif test_001.iloc[i, test_001.columns.get_loc('Flag_001')] == 'B'  and test_001.iloc[i-1, test_001.columns.get_loc('Flag_001')] == 'B':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = round(
            ((test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')] - 
              test_001.iloc[i, test_001.columns.get_loc('Signal_V_001')]) / 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_001.iloc[i, test_001.columns.get_loc('Flag_001')] == 'S' and test_001.iloc[i-1, test_001.columns.get_loc('Flag_001')] == 'S':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = round(
            ((test_001.iloc[i, test_001.columns.get_loc('Signal_V_001')] - 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) / 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_001.iloc[i, test_001.columns.get_loc('Flag_001')]):
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = np.nan
        
    elif pd.isna(test_001.iloc[i, test_001.columns.get_loc('Flag_001')]):
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_001')] = np.nan


# Initialize columns for long and short min/max values
test_001['Long_Min_001'] = np.nan
test_001['Long_Max_001'] = np.nan
test_001['Short_Min_001'] = np.nan
test_001['Short_Max_001'] = np.nan

# Track min and max values for long and short positions
cur_long_min_001 = None
cur_short_min_001 = None
cur_long_max_001 = None
cur_short_max_001 = None

for idx_001, row in test_001.iterrows():
    if row['Flag_001'] == 'B':
        if cur_long_min_001 is None or row['P_PCT_001'] < cur_long_min_001:
            cur_long_min_001 = row['P_PCT_001']
        if cur_long_max_001 is None or row['P_PCT_001'] > cur_long_max_001:
            cur_long_max_001 = row['P_PCT_001']
        test_001.at[idx_001, 'Long_Max_001'] = cur_long_max_001
        test_001.at[idx_001, 'Long_Min_001'] = cur_long_min_001
        cur_short_min_001 = None
        cur_short_max_001 = None
    elif row['Flag_001'] == 'S':
        if cur_short_min_001 is None or row['P_PCT_001'] < cur_short_min_001:
            cur_short_min_001 = row['P_PCT_001']
        if cur_short_max_001 is None or row['P_PCT_001'] > cur_short_max_001:
            cur_short_max_001 = row['P_PCT_001']
        test_001.at[idx_001, 'Short_Max_001'] = cur_short_max_001
        test_001.at[idx_001, 'Short_Min_001'] = cur_short_min_001
        cur_long_min_001 = None
        cur_long_max_001 = None

# Initialize columns for percentage calculations
test_001['LONG_PCT_001'] = np.nan
test_001['SHORT_PCT_001'] = np.nan

# Initialize 'LONG_PCT_001' and 'SHORT_PCT_001' columns with NaN values and ensure they're of type 'object'
test_001['LONG_PCT_001'] = np.nan  # Initialize with NaN
test_001['LONG_PCT_001'] = test_001['LONG_PCT_001'].astype('object')  # Convert to string type
test_001['SHORT_PCT_001'] = np.nan  # Initialize with NaN
test_001['SHORT_PCT_001'] = test_001['SHORT_PCT_001'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index_001, row in test_001.iterrows():
    if pd.notna(row['Long_Max_001']):
        if row['Long_Max_001'] != 0:
            long_pct_001 = (row['Long_Max_001'] - row['P_PCT_001']) / row['Long_Max_001'] * 100
        else:
            long_pct_001 = 0
        test_001.at[index_001, 'LONG_PCT_001'] = f"{long_pct_001:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_001']):
        if row['Short_Max_001'] != 0:
            short_pct_001 = (row['Short_Max_001'] - row['P_PCT_001']) / row['Short_Max_001'] * 100
        else:
            short_pct_001 = 0
        test_001.at[index_001, 'SHORT_PCT_001'] = f"{short_pct_001:.2f}"  # Store as formatted string


test_001['Sell_S_P_002'] = None
test_001['Buy_S_P_002'] = None
test_001['Sell_F_002'] = 0.0
test_001['Buy_F_002'] = 0.0

# Ensure column names have no leading or trailing whitespace
test_001.columns = test_001.columns.str.strip()

# Initialize flags and previous flag variable
triggered_short_002 = False
triggered_long_002 = False
prev_flag_002 = None

# Iterate through the rows of the DataFrame
for idx_002, row in test_001.iterrows():
    try:
        # Check if necessary columns are present before accessing them
        required_columns = ['Flag_001', 'Short_Max_001', 'Long_Max_001', 'SHORT_PCT_001', 'LONG_PCT_001', 'CLOSE', 'P_PCT_001']

        for col in required_columns:
            if col not in test_001.columns:
                raise KeyError(f"Missing column: {col}")

        # Get previous row (if exists)
        if idx_002 > test_001.index[0]:
            prev_row = test_001.loc[test_001.index[test_001.index.get_loc(idx_002) - 1]]
        else:
            prev_row = None

        # Check for "S" flag and conditions
        if row["Flag_001"] == "S" and prev_row is not None:
            prev_short_pct = float(prev_row["SHORT_PCT_001"])
            current_short_pct = float(row["SHORT_PCT_001"])

            # Convert P_PCT_001 to float and check if it's <= -0.6
            p_pct_value = float(row["P_PCT_001"])
            if p_pct_value <= -1:
                if not triggered_short_002:
                    Buy_S_P_002 = row['CLOSE']
                    test_001.at[idx_002, 'Buy_S_P_002'] = Buy_S_P_002
                    test_001.at[idx_002, 'Buy_F_002'] = Buy_S_P_002
                    triggered_short_002 = True
                    #print(f"S Flag Trigger: P_PCT_001={p_pct_value} <= -0.6, Buy signal at {Buy_S_P_002}")


        # Check for "B" flag and conditions
        elif row["Flag_001"] == "B" and prev_row is not None:
            prev_long_pct = float(prev_row["LONG_PCT_001"])
            current_long_pct = float(row["LONG_PCT_001"])

            # Convert P_PCT_001 to float and check if it's <= -0.6
            p_pct_value = float(row["P_PCT_001"])
            if p_pct_value <= -1:
                if not triggered_long_002:
                    Sell_S_P_002 = row["CLOSE"]
                    test_001.at[idx_002, "Sell_S_P_002"] = Sell_S_P_002
                    test_001.at[idx_002, "Sell_F_002"] = Sell_S_P_002
                    triggered_long_002 = True
                    #print(f"B Flag Trigger: P_PCT_001={p_pct_value} <= -0.6, Sell signal at {Sell_S_P_002}")


        # Reset flags if none of the conditions are met and flag has changed
        if row["Flag_001"] != prev_flag_002:
            triggered_short_002 = False  # Reset the trigger flags
            triggered_long_002 = False

        prev_flag_002 = row["Flag_001"]  # Update the previous flag

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx_002}. Available columns: {test_001.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx_002}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx_002}: {e}")



test_001['S_Flag_002'] = ''
test_001['B_Flag_002'] = ''

# Iterate through the DataFrame to set B_Flag_002
current_flag_1 = ''
current_flag_2 = ''
for index, row in test_001.iterrows():
    if row['Flag_001'] == 'B' and row['Sell_F_002'] > 0:
        current_flag_1 = 'S'
    elif row['Flag_001'] == 'S':
        current_flag_1 = np.nan
    test_001.at[index, 'S_Flag_002'] = current_flag_1
    
# Iterate through the DataFrame to set S_Flag_002
current_flag_3 = ''
current_flag_4 = ''
for index, row in test_001.iterrows():
    if row['Flag_001'] == 'S' and row['Buy_F_002'] > 0:
        current_flag_3 = 'B'        
    elif row['Flag_001'] == 'B':
        current_flag_3 = np.nan
    test_001.at[index, 'B_Flag_002'] = current_flag_3

# Replace 0 with NaN in the 'Sell_F_002' column and assign it to the 'Signal_V_002' column
test_001['Signal_V_002'] = test_001['Sell_F_002'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_002' column with corresponding 'Buy_F_002' values by replacing 0 with NaN
test_001['Signal_V_002'] = test_001['Signal_V_002'].fillna(test_001['Buy_F_002'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_001['Signal_V_002'] = test_001['Signal_V_002'].ffill()

# Initialize 'S_Signal_V_002' column
test_001['S_Signal_V_002'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set S_Signal_V_002
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_001.iterrows():
    if row['Flag_001'] == 'B' and row['Sell_F_002'] > 0:
        current_flag_3 = row['Signal_V_002']
    elif row['Flag_001'] == 'S':
        current_flag_3 = np.nan
    test_001.at[index, 'S_Signal_V_002'] = current_flag_3

# Initialize 'B_Signal_V_002' column
test_001['B_Signal_V_002'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_002
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_001.iterrows():
    if row['Flag_001'] == 'S' and row['Buy_F_002'] > 0:
        current_flag_3 = row['Signal_V_002']
    elif row['Flag_001'] == 'B':
        current_flag_3 = np.nan
    test_001.at[index, 'B_Signal_V_002'] = current_flag_3

# Drop the 'Signal_V_002' column
test_001.drop('Signal_V_002', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_002 column without raising warnings
test_001.loc[:, 'P_PCT_002'] = 0.0  # Initialize safely

for i in range(1, len(test_001)):
    # Check for the condition related to B_Flag_002
    if test_001.iloc[i, test_001.columns.get_loc('B_Flag_002')] == 'B' and test_001.iloc[i-1, test_001.columns.get_loc('B_Flag_002')] != 'B':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = 0
        
    # Check for the condition related to S_Flag_002
    elif test_001.iloc[i, test_001.columns.get_loc('S_Flag_002')] == 'S' and test_001.iloc[i-1, test_001.columns.get_loc('S_Flag_002')] != 'S':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = 0
        
    # Existing logic for calculating P_PCT_002 for B and S flags
    elif test_001.iloc[i, test_001.columns.get_loc('B_Flag_002')] == 'B'  and test_001.iloc[i-1, test_001.columns.get_loc('B_Flag_002')] == 'B':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = round(
            ((test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')] - 
              test_001.iloc[i, test_001.columns.get_loc('B_Signal_V_002')]) / 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_001.iloc[i, test_001.columns.get_loc('S_Flag_002')] == 'S' and test_001.iloc[i-1, test_001.columns.get_loc('S_Flag_002')] == 'S':
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = round(
            ((test_001.iloc[i, test_001.columns.get_loc('S_Signal_V_002')] - 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) / 
              test_001.iloc[i-1, test_001.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_001.iloc[i, test_001.columns.get_loc('S_Flag_002')]):
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = np.nan
        
    elif pd.isna(test_001.iloc[i, test_001.columns.get_loc('B_Flag_002')]):
        test_001.iloc[i, test_001.columns.get_loc('P_PCT_002')] = np.nan



# Ensure columns exist with appropriate dtype (float)
test_001['Sell_S_P_003'] = np.nan
test_001['Buy_S_P_003'] = np.nan
test_001['Sell_F_003'] = 0.0  # Explicitly set to float
test_001['Buy_F_003'] = 0.0   # Explicitly set to float

triggered_short_003 = False
triggered_long_003 = False
prev_flag_003 = None

# Remove whitespace from column names
test_001.columns = test_001.columns.str.strip()

# Ensure required columns exist
required_cols = ["Flag_001", "B_Flag_002", "P_PCT_002", "S_Flag_002", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_001.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx_003_1, row in test_001.iterrows():
    try:
        # Reset flags when Flag_001 changes
        if row["Flag_001"] != prev_flag_003:
            triggered_short_003 = False
            triggered_long_003 = False

        # Process "S" flag condition
        if row["Flag_001"] == "S":
            try:
                pct_value = pd.to_numeric(row["P_PCT_002"], errors="coerce")
                if row["B_Flag_002"] == "B" and pct_value < -1.2:
                    if not triggered_short_003:
                        Sell_S_P_003 = float(row["CLOSE"])  # Ensure it's explicitly float
                        test_001.at[idx_003_1, "Sell_S_P_003"] = Sell_S_P_003
                        test_001.at[idx_003_1, "Sell_F_003"] = Sell_S_P_003  # No warning now
                        triggered_short_003 = True
            except Exception as e:
                print(f"Error processing row index {idx_003_1} for 'S' flag: {e}")

        # Process "B" flag condition
        elif row["Flag_001"] == "B":
            try:
                pct_value = pd.to_numeric(row["P_PCT_002"], errors="coerce")
                if row["S_Flag_002"] == "S" and pct_value < -1.2:
                    if not triggered_long_003:
                        Buy_S_P_003 = float(row['CLOSE'])  # Ensure it's explicitly float
                        test_001.at[idx_003_1, 'Buy_S_P_003'] = Buy_S_P_003
                        test_001.at[idx_003_1, 'Buy_F_003'] = Buy_S_P_003  # No warning now
                        triggered_long_003 = True
            except Exception as e:
                print(f"Error processing row index {idx_003_1} for 'B' flag: {e}")

        prev_flag_003 = row["Flag_001"]

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx_003_1}. Available columns: {test_001.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx_003_1}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx_003_1}: {e}")


# Calculate combined signals with error handling
try:
    test_001['Combined_Buy_01'] = test_001[['Buy_F_001', 'Buy_F_002', 'Buy_F_003']].fillna(0).sum(axis=1)
    test_001['Combined_Sell_01'] = test_001[['Sell_F_001', 'Sell_F_002', 'Sell_F_003']].fillna(0).sum(axis=1)
except Exception as e:
    #print(f"Error creating combined columns: {e}")
    print(f"{formatted_time} - Error creating combined columns: {e}")  
    raise

# Initialize lists for signals
BUY_004 = []
SELL_004 = []
b_list_004 = []
s_list_004 = []

# Initialize position flag
position_004 = False

# Process signals
try:
    for i in range(len(test_001)):
        current_row = test_001.iloc[i]
        
        if not position_004:
            if current_row['Combined_Buy_01'] > 0 and current_row['Combined_Sell_01'] == 0:
                BUY_004.append(current_row['CLOSE'])
                b_list_004.append(current_row['CLOSE'])
                SELL_004.append(np.nan)
                s_list_004.append(0)
                position_004 = True
            else:
                BUY_004.append(np.nan)
                b_list_004.append(0)
                SELL_004.append(np.nan)
                s_list_004.append(0)
        else:
            if current_row['Combined_Sell_01'] > 0 and current_row['Combined_Buy_01'] == 0:
                BUY_004.append(np.nan)
                b_list_004.append(0)
                SELL_004.append(current_row['CLOSE'])
                s_list_004.append(current_row['CLOSE'])
                position_004 = False
            else:
                BUY_004.append(np.nan)
                b_list_004.append(0)
                SELL_004.append(np.nan)
                s_list_004.append(0)

    # Add signals to DataFrame with error handling
    test_001['Buy_S_P_004'] = BUY_004
    test_001['Sell_S_P_004'] = SELL_004
    test_001['Buy_F_004'] = b_list_004
    test_001['Sell_F_004'] = s_list_004

    # Verify columns were created
    new_columns = ['Buy_S_P_004', 'Sell_S_P_004', 'Buy_F_004', 'Sell_F_004']
    for col in new_columns:
        if col not in test_001.columns:
            print(f"{formatted_time} - Warning: Column {col} was not created")            
            
except Exception as e:
    print(str(formatted_time) + ' - ' + "Error during signal processing ...")
    raise

test_002 = test_001.copy()

# Replace 0 with NaN in the 'Sell_F_004' column and assign it to the 'Signal_V_004' column
test_002['Signal_V_004'] = test_002['Sell_F_004'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_004' column with corresponding 'Buy_F_004' values by replacing 0 with NaN, and apply forward-fill
test_002['Signal_V_004'] = test_002['Signal_V_004'].fillna(test_002['Buy_F_004'].replace(0, np.nan)).ffill()

# Initialize 'Flag_004' column with NaN values and ensure it's of type 'object' (string)
test_002['Flag_004'] = np.nan  # Initialize with NaN
test_002['Flag_004'] = test_002['Flag_004'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_004 and 'S' for Sell_F_004 in the 'Flag_004' column
test_002.loc[test_002['Buy_F_004'] != 0, 'Flag_004'] = 'B'
test_002.loc[test_002['Sell_F_004'] != 0, 'Flag_004'] = 'S'

# Forward-fill the NaN values in the 'Flag_004' column
test_002['Flag_004'] = test_002['Flag_004'].ffill()

# Initialize 'P_PCT_004' column with 0
test_002['P_PCT_004'] = 0.0

# Signal PCT
# Initialize the P_PCT_004 column without raising warnings
test_002.loc[:, 'P_PCT_004'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_004
    if test_002.iloc[i, test_002.columns.get_loc('Flag_004')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_004')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = 0
        
    # Check for the condition related to S_Flag_004
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_004')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_004')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = 0
        
    # Existing logic for calculating P_PCT_004 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_004')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('Flag_004')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('Signal_V_004')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_004')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_004')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('Signal_V_004')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('Flag_004')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('Flag_004')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_004')] = np.nan


# Initialize columns for long and short min/max values
test_002['Long_Min_004'] = np.nan
test_002['Long_Max_004'] = np.nan
test_002['Short_Min_004'] = np.nan
test_002['Short_Max_004'] = np.nan

# Track min and max values for long and short positions
cur_long_min_004 = None
cur_short_min_004 = None
cur_long_max_004 = None
cur_short_max_004 = None

for idx_004, row in test_002.iterrows():
    if row['Flag_004'] == 'B':
        if cur_long_min_004 is None or row['P_PCT_004'] < cur_long_min_004:
            cur_long_min_004 = row['P_PCT_004']
        if cur_long_max_004 is None or row['P_PCT_004'] > cur_long_max_004:
            cur_long_max_004 = row['P_PCT_004']
        test_002.at[idx_004, 'Long_Max_004'] = cur_long_max_004
        test_002.at[idx_004, 'Long_Min_004'] = cur_long_min_004
        cur_short_min_004 = None
        cur_short_max_004 = None
    elif row['Flag_004'] == 'S':
        if cur_short_min_004 is None or row['P_PCT_004'] < cur_short_min_004:
            cur_short_min_004 = row['P_PCT_004']
        if cur_short_max_004 is None or row['P_PCT_004'] > cur_short_max_004:
            cur_short_max_004 = row['P_PCT_004']
        test_002.at[idx_004, 'Short_Max_004'] = cur_short_max_004
        test_002.at[idx_004, 'Short_Min_004'] = cur_short_min_004
        cur_long_min_004 = None
        cur_long_max_004 = None

# Initialize columns for percentage calculations
test_002['LONG_PCT_004'] = np.nan
test_002['SHORT_PCT_004'] = np.nan

# Initialize 'LONG_PCT_004' and 'SHORT_PCT_004' columns with NaN values and ensure they're of type 'object'
test_002['LONG_PCT_004'] = np.nan  # Initialize with NaN
test_002['LONG_PCT_004'] = test_002['LONG_PCT_004'].astype('object')  # Convert to string type
test_002['SHORT_PCT_004'] = np.nan  # Initialize with NaN
test_002['SHORT_PCT_004'] = test_002['SHORT_PCT_004'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index_004, row in test_002.iterrows():
    if pd.notna(row['Long_Max_004']):
        if row['Long_Max_004'] != 0:
            long_pct_004 = (row['Long_Max_004'] - row['P_PCT_004']) / row['Long_Max_004'] * 100
        else:
            long_pct_004 = 0
        test_002.at[index_004, 'LONG_PCT_004'] = f"{long_pct_004:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_004']):
        if row['Short_Max_004'] != 0:
            short_pct_004 = (row['Short_Max_004'] - row['P_PCT_004']) / row['Short_Max_004'] * 100
        else:
            short_pct_004 = 0
        test_002.at[index_004, 'SHORT_PCT_004'] = f"{short_pct_004:.2f}"  # Store as formatted string

test_002['Sell_S_P_005'] = None
test_002['Buy_S_P_005'] = None
test_002['Sell_F_005'] = 0.0
test_002['Buy_F_005'] = 0.0

# Ensure column names have no leading or trailing whitespace
test_002.columns = test_002.columns.str.strip()

# Initialize flags and previous flag variable
triggered_short_005 = False
triggered_long_005 = False
prev_flag_005 = None

# Iterate through the rows of the DataFrame
for idx, row in test_002.iterrows():
    try:
        # Check if necessary columns are present before accessing them
        required_columns = ['Flag_004', 'Short_Max_004', 'Long_Max_004', 'SHORT_PCT_004', 'LONG_PCT_004', 'CLOSE', 'P_PCT_004']

        for col in required_columns:
            if col not in test_002.columns:
                raise KeyError(f"Missing column: {col}")

        # Get previous row (if exists)
        if idx > test_002.index[0]:
            prev_row = test_002.loc[test_002.index[test_002.index.get_loc(idx) - 1]]
        else:
            prev_row = None

        # Check for "S" flag and conditions
        if row["Flag_004"] == "S" and prev_row is not None:
            prev_short_pct = float(prev_row["SHORT_PCT_004"])
            current_short_pct = float(row["SHORT_PCT_004"])

            # Convert P_PCT_004 to float and check if it's <= -0.6
            p_pct_value = float(row["P_PCT_004"])
            if p_pct_value <= -1:
                if not triggered_short_005:
                    Buy_S_P_005 = row['CLOSE']
                    test_002.at[idx, 'Buy_S_P_005'] = Buy_S_P_005
                    test_002.at[idx, 'Buy_F_005'] = Buy_S_P_005
                    triggered_short_005 = True
                    #print(f"S Flag Trigger: P_PCT_004={p_pct_value} <= -0.6, Buy signal at {Buy_S_P_005}")


        # Check for "B" flag and conditions
        elif row["Flag_004"] == "B" and prev_row is not None:
            prev_long_pct = float(prev_row["LONG_PCT_004"])
            current_long_pct = float(row["LONG_PCT_004"])

            # Convert P_PCT_004 to float and check if it's <= -0.6
            p_pct_value = float(row["P_PCT_004"])
            if p_pct_value <= -1:
                if not triggered_long_005:
                    Sell_S_P_005 = row["CLOSE"]
                    test_002.at[idx, "Sell_S_P_005"] = Sell_S_P_005
                    test_002.at[idx, "Sell_F_005"] = Sell_S_P_005
                    triggered_long_005 = True
                    #print(f"B Flag Trigger: P_PCT_004={p_pct_value} <= -0.6, Sell signal at {Sell_S_P_005}")


        # Reset flags if none of the conditions are met and flag has changed
        if row["Flag_004"] != prev_flag_005:
            triggered_short_005 = False  # Reset the trigger flags
            triggered_long_005 = False

        prev_flag_005 = row["Flag_004"]  # Update the previous flag

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx}. Available columns: {test_002.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx}: {e}")



test_002['S_Flag_005'] = ''
test_002['B_Flag_005'] = ''

# Iterate through the DataFrame to set B_Flag_005
current_flag_1 = ''
current_flag_2 = ''
for index, row in test_002.iterrows():
    if row['Flag_004'] == 'B' and row['Sell_F_005'] > 0:
        current_flag_1 = 'S'
    elif row['Flag_004'] == 'S':
        current_flag_1 = np.nan
    test_002.at[index, 'S_Flag_005'] = current_flag_1
    
# Iterate through the DataFrame to set S_Flag_005
current_flag_3 = ''
current_flag_4 = ''
for index, row in test_002.iterrows():
    if row['Flag_004'] == 'S' and row['Buy_F_005'] > 0:
        current_flag_3 = 'B'        
    elif row['Flag_004'] == 'B':
        current_flag_3 = np.nan
    test_002.at[index, 'B_Flag_005'] = current_flag_3

# Replace 0 with NaN in the 'Sell_F_005' column and assign it to the 'Signal_V_005' column
test_002['Signal_V_005'] = test_002['Sell_F_005'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_005' column with corresponding 'Buy_F_005' values by replacing 0 with NaN
test_002['Signal_V_005'] = test_002['Signal_V_005'].fillna(test_002['Buy_F_005'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_002['Signal_V_005'] = test_002['Signal_V_005'].ffill()

# Initialize 'S_Signal_V_005' column
test_002['S_Signal_V_005'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set S_Signal_V_005
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['Flag_004'] == 'B' and row['Sell_F_005'] > 0:
        current_flag_3 = row['Signal_V_005']
    elif row['Flag_004'] == 'S':
        current_flag_3 = np.nan
    test_002.at[index, 'S_Signal_V_005'] = current_flag_3

# Initialize 'B_Signal_V_005' column
test_002['B_Signal_V_005'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_005
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['Flag_004'] == 'S' and row['Buy_F_005'] > 0:
        current_flag_3 = row['Signal_V_005']
    elif row['Flag_004'] == 'B':
        current_flag_3 = np.nan
    test_002.at[index, 'B_Signal_V_005'] = current_flag_3

# Drop the 'Signal_V_005' column
test_002.drop('Signal_V_005', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_005 column without raising warnings
test_002.loc[:, 'P_PCT_005'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_005
    if test_002.iloc[i, test_002.columns.get_loc('B_Flag_005')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_005')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = 0
        
    # Check for the condition related to S_Flag_005
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_005')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_005')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = 0
        
    # Existing logic for calculating P_PCT_005 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('B_Flag_005')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_005')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('B_Signal_V_005')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_005')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_005')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('S_Signal_V_005')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('S_Flag_005')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('B_Flag_005')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_005')] = np.nan


# Ensure columns exist with appropriate dtype (float)
test_002['Sell_S_P_006'] = np.nan
test_002['Buy_S_P_006'] = np.nan
test_002['Sell_F_006'] = 0.0  # Explicitly set to float
test_002['Buy_F_006'] = 0.0   # Explicitly set to float

triggered_short_006 = False
triggered_long_006 = False
prev_flag_006 = None

# Remove whitespace from column names
test_002.columns = test_002.columns.str.strip()

# Ensure required columns exist
required_cols = ["Flag_004", "B_Flag_005", "P_PCT_005", "S_Flag_005", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_002.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx, row in test_002.iterrows():
    try:
        # Reset flags when Flag_004 changes
        if row["Flag_004"] != prev_flag_006:
            triggered_short_006 = False
            triggered_long_006 = False

        # Process "S" flag condition
        if row["Flag_004"] == "S":
            try:
                pct_value = pd.to_numeric(row["P_PCT_005"], errors="coerce")
                if row["B_Flag_005"] == "B" and pct_value < -2:
                    if not triggered_short_006:
                        Sell_S_P_006 = float(row["CLOSE"])  # Ensure it's explicitly float
                        test_002.at[idx, "Sell_S_P_006"] = Sell_S_P_006
                        test_002.at[idx, "Sell_F_006"] = Sell_S_P_006  # No warning now
                        triggered_short_006 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'S' flag: {e}")

        # Process "B" flag condition
        elif row["Flag_004"] == "B":
            try:
                pct_value = pd.to_numeric(row["P_PCT_005"], errors="coerce")
                if row["S_Flag_005"] == "S" and pct_value < -2:
                    if not triggered_long_006:
                        Buy_S_P_006 = float(row['CLOSE'])  # Ensure it's explicitly float
                        test_002.at[idx, 'Buy_S_P_006'] = Buy_S_P_006
                        test_002.at[idx, 'Buy_F_006'] = Buy_S_P_006  # No warning now
                        triggered_long_006 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'B' flag: {e}")

        prev_flag_006 = row["Flag_004"]

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx}. Available columns: {test_002.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx}: {e}")



# Calculate combined signals with error handling
try:
    test_002['Combined_Buy_02'] = test_002[['Buy_F_004', 'Buy_F_005', 'Buy_F_006']].fillna(0).sum(axis=1)
    test_002['Combined_Sell_02'] = test_002[['Sell_F_004', 'Sell_F_005', 'Sell_F_006']].fillna(0).sum(axis=1)
except Exception as e:
    #print(f"Error creating combined columns: {e}")
    print(f"{formatted_time} - Error creating combined columns: {e}")  
    raise


# Initialize lists for signals
BUY_007 = []
SELL_007 = []
b_list_007 = []
s_list_007 = []

# Initialize position flag
position_007 = False

# Process signals
try:
    for i in range(len(test_002)):
        current_row = test_002.iloc[i]
        
        if not position_007:
            if current_row['Combined_Buy_02'] > 0 and current_row['Combined_Sell_02'] == 0:
                BUY_007.append(current_row['CLOSE'])
                b_list_007.append(current_row['CLOSE'])
                SELL_007.append(np.nan)
                s_list_007.append(0)
                position_007 = True
            else:
                BUY_007.append(np.nan)
                b_list_007.append(0)
                SELL_007.append(np.nan)
                s_list_007.append(0)
        else:
            if current_row['Combined_Sell_02'] > 0 and current_row['Combined_Buy_02'] == 0:
                BUY_007.append(np.nan)
                b_list_007.append(0)
                SELL_007.append(current_row['CLOSE'])
                s_list_007.append(current_row['CLOSE'])
                position_007 = False
            else:
                BUY_007.append(np.nan)
                b_list_007.append(0)
                SELL_007.append(np.nan)
                s_list_007.append(0)

    # Add signals to DataFrame with error handling
    test_002['Buy_S_P_007'] = BUY_007
    test_002['Sell_S_P_007'] = SELL_007
    test_002['Buy_F_007'] = b_list_007
    test_002['Sell_F_007'] = s_list_007

    # Verify columns were created
    new_columns = ['Buy_S_P_007', 'Sell_S_P_007', 'Buy_F_007', 'Sell_F_007']
    for col in new_columns:
        if col not in test_002.columns:
            print(f"{formatted_time} - Warning: Column {col} was not created")            
            
except Exception as e:
    print(str(formatted_time) + ' - ' + "Error during signal processing ...")
    raise



# Replace 0 with NaN in the 'Sell_F_007' column and assign it to the 'Signal_V_007' column
test_002['Signal_V_007'] = test_002['Sell_F_007'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_007' column with corresponding 'Buy_F_007' values by replacing 0 with NaN, and apply forward-fill
test_002['Signal_V_007'] = test_002['Signal_V_007'].fillna(test_002['Buy_F_007'].replace(0, np.nan)).ffill()

# Initialize 'Flag_007' column with NaN values and ensure it's of type 'object' (string)
test_002['Flag_007'] = np.nan  # Initialize with NaN
test_002['Flag_007'] = test_002['Flag_007'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_007 and 'S' for Sell_F_007 in the 'Flag_007' column
test_002.loc[test_002['Buy_F_007'] != 0, 'Flag_007'] = 'B'
test_002.loc[test_002['Sell_F_007'] != 0, 'Flag_007'] = 'S'

# Forward-fill the NaN values in the 'Flag_007' column
test_002['Flag_007'] = test_002['Flag_007'].ffill()

# Initialize 'P_PCT_007' column with 0
test_002['P_PCT_007'] = 0.0

# Signal PCT
# Initialize the P_PCT_007 column without raising warnings
test_002.loc[:, 'P_PCT_007'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_007
    if test_002.iloc[i, test_002.columns.get_loc('Flag_007')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_007')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = 0
        
    # Check for the condition related to S_Flag_007
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_007')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_007')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = 0
        
    # Existing logic for calculating P_PCT_007 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_007')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('Flag_007')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('Signal_V_007')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('Flag_007')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('Flag_007')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('Signal_V_007')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('Flag_007')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('Flag_007')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_007')] = np.nan


# Initialize columns for long and short min/max values
test_002['Long_Min_007'] = np.nan
test_002['Long_Max_007'] = np.nan
test_002['Short_Min_007'] = np.nan
test_002['Short_Max_007'] = np.nan

# Track min and max values for long and short positions
cur_long_min_007 = None
cur_short_min_007 = None
cur_long_max_007 = None
cur_short_max_007 = None

for idx, row in test_002.iterrows():
    if row['Flag_007'] == 'B':
        if cur_long_min_007 is None or row['P_PCT_007'] < cur_long_min_007:
            cur_long_min_007 = row['P_PCT_007']
        if cur_long_max_007 is None or row['P_PCT_007'] > cur_long_max_007:
            cur_long_max_007 = row['P_PCT_007']
        test_002.at[idx, 'Long_Max_007'] = cur_long_max_007
        test_002.at[idx, 'Long_Min_007'] = cur_long_min_007
        cur_short_min_007 = None
        cur_short_max_007 = None
    elif row['Flag_007'] == 'S':
        if cur_short_min_007 is None or row['P_PCT_007'] < cur_short_min_007:
            cur_short_min_007 = row['P_PCT_007']
        if cur_short_max_007 is None or row['P_PCT_007'] > cur_short_max_007:
            cur_short_max_007 = row['P_PCT_007']
        test_002.at[idx, 'Short_Max_007'] = cur_short_max_007
        test_002.at[idx, 'Short_Min_007'] = cur_short_min_007
        cur_long_min_007 = None
        cur_long_max_007 = None


# Initialize columns for percentage calculations
test_002['LONG_PCT_007'] = np.nan
test_002['SHORT_PCT_007'] = np.nan

# Initialize 'LONG_PCT_007' and 'SHORT_PCT_007' columns with NaN values and ensure they're of type 'object'
test_002['LONG_PCT_007'] = np.nan  # Initialize with NaN
test_002['LONG_PCT_007'] = test_002['LONG_PCT_007'].astype('object')  # Convert to string type
test_002['SHORT_PCT_007'] = np.nan  # Initialize with NaN
test_002['SHORT_PCT_007'] = test_002['SHORT_PCT_007'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index, row in test_002.iterrows():
    if pd.notna(row['Long_Max_007']):
        if row['Long_Max_007'] != 0:
            long_pct_007 = (row['Long_Max_007'] - row['P_PCT_007']) / row['Long_Max_007'] * 100
        else:
            long_pct_007 = 0
        test_002.at[index, 'LONG_PCT_007'] = f"{long_pct_007:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_007']):
        if row['Short_Max_007'] != 0:
            short_pct_007 = (row['Short_Max_007'] - row['P_PCT_007']) / row['Short_Max_007'] * 100
        else:
            short_pct_007 = 0
        test_002.at[index, 'SHORT_PCT_007'] = f"{short_pct_007:.2f}"  # Store as formatted string

#TP
# Initialize columns
test_002['Sell_S_P_008'] = None
test_002['Buy_S_P_008'] = None
test_002['Sell_F_008'] = 0.0
test_002['Buy_F_008'] = 0.0

# Clean column names
test_002.columns = test_002.columns.str.strip()

# Initialize flags
triggered_short = False
triggered_long = False
prev_flag = None

for idx, row in test_002.iterrows():
    try:
        # Verify required columns exist
        required_cols = ['Flag_007', 'Short_Max_007', 'Long_Max_007', 
                        'SHORT_PCT_007', 'LONG_PCT_007', 'CLOSE']
        missing_cols = [col for col in required_cols if col not in test_002.columns]
        if missing_cols:
            raise KeyError(f"Missing columns: {missing_cols}")

        # Get previous row if exists
        prev_row = test_002.loc[test_002.index[test_002.index.get_loc(idx)-1]] if idx > test_002.index[0] else None

        # Reset triggers when flag changes
        if prev_flag is not None and row['Flag_007'] != prev_flag:
            triggered_short = False
            triggered_long = False

        # SELL LOGIC (Triggered on B flag)
        if row['Flag_007'] == 'B' and (prev_row['Flag_007'] == 'B') and not triggered_long:
            long_max = float(row['Long_Max_007'])
            long_pct = float(row['LONG_PCT_007'])
            p_pct = float(row['P_PCT_007'])
            
            # Define sell conditions
            sell_conditions = [             
                (4 <= long_max <= 5) and (long_pct >= 95),
                (5 < long_max <= 7) and (long_pct >= 90),
                (7 < long_max <= 9) and (long_pct >= 65),
                (9 < long_max <= 12) and (long_pct >= 58),
                (12 < long_max <= 16) and (long_pct >= 55), 
                (16 < long_max <= 22) and (long_pct >= 50),
                (22 < long_max <= 25) and (long_pct >= 45),
                (25 < long_max <= 27.5) and (long_pct >= 52),
                (27.5 < long_max <= 30) and (long_pct >= 42),
                (30 < long_max <= 35) and (long_pct >= 40),
                (35 < long_max <= 40) and (long_pct >= 30),                
                (40 < long_max <= 50) and (long_pct >= 20), 
                (long_max > 50) and (long_pct >= 17),
                (p_pct < -2)
            ]
            
            # Trigger sell if any condition met
            if any(sell_conditions):
                sell_price = row['CLOSE']
                test_002.at[idx, 'Sell_S_P_008'] = sell_price
                test_002.at[idx, 'Sell_F_008'] = sell_price
                triggered_long = True

        # BUY LOGIC (Triggered on S flag)
        elif row['Flag_007'] == 'S' and (prev_row['Flag_007'] == 'S') and not triggered_short:
            short_max = float(row['Short_Max_007'])
            short_pct = float(row['SHORT_PCT_007'])
            p_pct = float(row['P_PCT_007'])
            
            # Define buy conditions
            buy_conditions = [              
                (4 <= short_max <= 5) and (short_pct >= 95),
                (5 < short_max <= 7) and (short_pct >= 90),
                (7 < short_max <= 9) and (short_pct >= 65),
                (9 < short_max <= 12) and (short_pct >= 58),
                (12 < short_max <= 16) and (short_pct >= 55), 
                (16 < short_max <= 22) and (short_pct >= 50),
                (22 < short_max <= 25) and (short_pct >= 45),
                (25 < short_max <= 27.5) and (short_pct >= 52),
                (27.5 < short_max <= 30) and (short_pct >= 42),
                (30 < short_max <= 35) and (short_pct >= 40),
                (35 < short_max <= 40) and (short_pct >= 30),                
                (40 < short_max <= 50) and (short_pct >= 20), 
                (short_max > 50) and (short_pct >= 17), 
                (p_pct < -2)
            ]
            
            # Trigger buy if any condition met
            if any(buy_conditions):
                buy_price = row['CLOSE']
                test_002.at[idx, 'Buy_S_P_008'] = buy_price
                test_002.at[idx, 'Buy_F_008'] = buy_price
                triggered_short = True

        # Update previous flag
        prev_flag = row['Flag_007']

    except KeyError as e:
        print(f"KeyError at index {idx}: {e}")
    except ValueError as e:
        print(f"ValueError at index {idx}: {e}")
    except Exception as e:
        print(f"Unexpected error at index {idx}: {e}")


test_002['S_Flag_008'] = ''
test_002['B_Flag_008'] = ''

# Iterate through the DataFrame to set B_Flag_008
current_flag_1 = ''
current_flag_2 = ''
for index, row in test_002.iterrows():
    if row['Flag_007'] == 'B' and row['Sell_F_008'] > 0:
        current_flag_1 = 'S'
    elif row['Flag_007'] == 'S':
        current_flag_1 = np.nan
    test_002.at[index, 'S_Flag_008'] = current_flag_1
    
# Iterate through the DataFrame to set S_Flag_008
current_flag_3 = ''
current_flag_4 = ''
for index, row in test_002.iterrows():
    if row['Flag_007'] == 'S' and row['Buy_F_008'] > 0:
        current_flag_3 = 'B'        
    elif row['Flag_007'] == 'B':
        current_flag_3 = np.nan
    test_002.at[index, 'B_Flag_008'] = current_flag_3

# Replace 0 with NaN in the 'Sell_F_008' column and assign it to the 'Signal_V_008' column
test_002['Signal_V_008'] = test_002['Sell_F_008'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_008' column with corresponding 'Buy_F_008' values by replacing 0 with NaN
test_002['Signal_V_008'] = test_002['Signal_V_008'].fillna(test_002['Buy_F_008'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_002['Signal_V_008'] = test_002['Signal_V_008'].ffill()

# Initialize 'S_Signal_V_008' column
test_002['S_Signal_V_008'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set S_Signal_V_008
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['Flag_007'] == 'B' and row['Sell_F_008'] > 0:
        current_flag_3 = row['Signal_V_008']
    elif row['Flag_007'] == 'S':
        current_flag_3 = np.nan
    test_002.at[index, 'S_Signal_V_008'] = current_flag_3

# Initialize 'B_Signal_V_008' column
test_002['B_Signal_V_008'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_008
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['Flag_007'] == 'S' and row['Buy_F_008'] > 0:
        current_flag_3 = row['Signal_V_008']
    elif row['Flag_007'] == 'B':
        current_flag_3 = np.nan
    test_002.at[index, 'B_Signal_V_008'] = current_flag_3

# Drop the 'Signal_V_008' column
test_002.drop('Signal_V_008', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_008 column without raising warnings
test_002.loc[:, 'P_PCT_008'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_008
    if test_002.iloc[i, test_002.columns.get_loc('B_Flag_008')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_008')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = 0
        
    # Check for the condition related to S_Flag_008
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_008')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_008')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = 0
        
    # Existing logic for calculating P_PCT_008 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('B_Flag_008')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_008')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('B_Signal_V_008')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_008')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_008')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('S_Signal_V_008')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('S_Flag_008')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('B_Flag_008')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_008')] = np.nan

#TP SL impmented newly on Sep 2025. 
#---------------------------------------------------------------
# Ensure columns exist with appropriate dtype (float)
test_002['Sell_S_P_009'] = np.nan
test_002['Buy_S_P_009'] = np.nan
test_002['Sell_F_009'] = 0.0  # Explicitly set to float
test_002['Buy_F_009'] = 0.0   # Explicitly set to float

triggered_short_009 = False
triggered_long_009 = False
prev_flag_009 = None

# Remove whitespace from column names
test_002.columns = test_002.columns.str.strip()

# Ensure required columns exist
required_cols = ["Flag_007", "B_Flag_008", "P_PCT_008", "S_Flag_008", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_002.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx, row in test_002.iterrows():
    try:
        # Reset flags when Flag_007 changes
        if row["Flag_007"] != prev_flag_009:
            triggered_short_009 = False
            triggered_long_009 = False

        # Process "S" flag condition
        if row["Flag_007"] == "S":
            try:
                pct_value = pd.to_numeric(row["P_PCT_008"], errors="coerce")
                if row["B_Flag_008"] == "B" and pct_value < -1.4:
                    if not triggered_short_009:
                        Sell_S_P_009 = float(row["CLOSE"])  # Ensure it's explicitly float
                        test_002.at[idx, "Sell_S_P_009"] = Sell_S_P_009
                        test_002.at[idx, "Sell_F_009"] = Sell_S_P_009  # No warning now
                        triggered_short_009 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'S' flag: {e}")

        # Process "B" flag condition
        elif row["Flag_007"] == "B":
            try:
                pct_value = pd.to_numeric(row["P_PCT_008"], errors="coerce")
                if row["S_Flag_008"] == "S" and pct_value < -1.4:
                    if not triggered_long_009:
                        Buy_S_P_009 = float(row['CLOSE'])  # Ensure it's explicitly float
                        test_002.at[idx, 'Buy_S_P_009'] = Buy_S_P_009
                        test_002.at[idx, 'Buy_F_009'] = Buy_S_P_009  # No warning now
                        triggered_long_009 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'B' flag: {e}")

        prev_flag_009 = row["Flag_007"]

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx}. Available columns: {test_002.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx}: {e}")


test_002['S_Flag_009'] = ''
test_002['B_Flag_009'] = ''

# Iterate through the DataFrame to set S_Flag_009
current_flag_1 = ''
for index, row in test_002.iterrows():
    if row['B_Flag_008'] == 'B' and row['Sell_F_009'] > 0:
        current_flag_1 = 'S'
    elif pd.isna(row['B_Flag_008']):
        current_flag_1 = np.nan
    test_002.at[index, 'S_Flag_009'] = current_flag_1

# Iterate through the DataFrame to set B_Flag_009
current_flag_3 = ''
for index, row in test_002.iterrows():
    if row['S_Flag_008'] == 'S' and row['Buy_F_009'] > 0:
        current_flag_3 = 'B'
    elif pd.isna(row['S_Flag_008']):
        current_flag_3 = np.nan
    test_002.at[index, 'B_Flag_009'] = current_flag_3


# Replace 0 with NaN in the 'Sell_F_009' column and assign it to the 'Signal_V_009' column
test_002['Signal_V_009'] = test_002['Sell_F_009'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_009' column with corresponding 'Buy_F_009' values by replacing 0 with NaN
test_002['Signal_V_009'] = test_002['Signal_V_009'].fillna(test_002['Buy_F_009'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_002['Signal_V_009'] = test_002['Signal_V_009'].ffill()

# Initialize 'S_Signal_V_009' column
test_002['S_Signal_V_009'] = np.nan  # Use NaN instead of empty string for consistency


# Iterate through the DataFrame to set S_Signal_V_009
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['B_Flag_008'] == 'B' and row['Sell_F_009'] > 0:
        current_flag_3 = row['Signal_V_009']
    elif pd.isna(row['B_Flag_008']):
        current_flag_3 = np.nan
    test_002.at[index, 'S_Signal_V_009'] = current_flag_3

# Initialize 'B_Signal_V_009' column
test_002['B_Signal_V_009'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_009
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['S_Flag_008'] == 'S' and row['Buy_F_009'] > 0:
        current_flag_3 = row['Signal_V_009']
    elif pd.isna(row['S_Flag_008']):
        current_flag_3 = np.nan
    test_002.at[index, 'B_Signal_V_009'] = current_flag_3

# Drop the 'Signal_V_009' column
test_002.drop('Signal_V_009', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_009 column without raising warnings
test_002.loc[:, 'P_PCT_009'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_009
    if test_002.iloc[i, test_002.columns.get_loc('B_Flag_009')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_009')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = 0
        
    # Check for the condition related to S_Flag_009
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_009')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_009')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = 0
        
    # Existing logic for calculating P_PCT_009 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('B_Flag_009')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_009')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('B_Signal_V_009')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_009')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_009')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('S_Signal_V_009')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('S_Flag_009')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('B_Flag_009')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_009')] = np.nan

# Ensure columns exist with appropriate dtype (float)
test_002['Sell_S_P_010'] = np.nan
test_002['Buy_S_P_010'] = np.nan
test_002['Sell_F_010'] = 0.0  # Explicitly set to float
test_002['Buy_F_010'] = 0.0   # Explicitly set to float

triggered_short_010 = False
triggered_long_010 = False
prev_flag_010 = None  # Now will store (S_Flag_008, B_Flag_008)

# Remove whitespace from column names
test_002.columns = test_002.columns.str.strip()

# Ensure required columns exist
required_cols = ["S_Flag_008", "B_Flag_008", "B_Flag_009", 
                 "P_PCT_009", "S_Flag_009", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_002.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx, row in test_002.iterrows():
    try:
        # Reset flags when flag combination changes
        current_flag_state = (row["S_Flag_008"], row["B_Flag_008"])
        if current_flag_state != prev_flag_010:
            triggered_short_010 = False
            triggered_long_010 = False

        # Process "S" flag condition
        if row["S_Flag_008"] == "S":
            pct_value = pd.to_numeric(row["P_PCT_009"], errors="coerce")
            if row["B_Flag_009"] == "B" and pct_value < -1.5:
                if not triggered_short_010:
                    Sell_S_P_010 = float(row["CLOSE"])  # Explicitly float
                    test_002.at[idx, "Sell_S_P_010"] = Sell_S_P_010
                    test_002.at[idx, "Sell_F_010"] = Sell_S_P_010
                    triggered_short_010 = True

        # Process "B" flag condition
        if row["B_Flag_008"] == "B":
            pct_value = pd.to_numeric(row["P_PCT_009"], errors="coerce")
            if row["S_Flag_009"] == "S" and pct_value < -1.5:
                if not triggered_long_010:
                    Buy_S_P_010 = float(row["CLOSE"])
                    test_002.at[idx, "Buy_S_P_010"] = Buy_S_P_010
                    test_002.at[idx, "Buy_F_010"] = Buy_S_P_010
                    triggered_long_010 = True

        prev_flag_010 = current_flag_state

    except Exception as e:
        print(f"Error at row {idx}: {e}")

#---------------------------------------------------------------------------------

test_002['S_Flag_010'] = ''
test_002['B_Flag_010'] = ''

# Iterate through the DataFrame to set S_Flag_010
current_flag_1 = ''
for index, row in test_002.iterrows():
    if row['B_Flag_009'] == 'B' and row['Sell_F_010'] > 0:
        current_flag_1 = 'S'
    elif pd.isna(row['B_Flag_009']):
        current_flag_1 = np.nan
    test_002.at[index, 'S_Flag_010'] = current_flag_1

# Iterate through the DataFrame to set B_Flag_010
current_flag_3 = ''
for index, row in test_002.iterrows():
    if row['S_Flag_009'] == 'S' and row['Buy_F_010'] > 0:
        current_flag_3 = 'B'
    elif pd.isna(row['S_Flag_009']):
        current_flag_3 = np.nan
    test_002.at[index, 'B_Flag_010'] = current_flag_3


# Replace 0 with NaN in the 'Sell_F_010' column and assign it to the 'Signal_V_010' column
test_002['Signal_V_010'] = test_002['Sell_F_010'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_010' column with corresponding 'Buy_F_010' values by replacing 0 with NaN
test_002['Signal_V_010'] = test_002['Signal_V_010'].fillna(test_002['Buy_F_010'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_002['Signal_V_010'] = test_002['Signal_V_010'].ffill()

# Initialize 'S_Signal_V_010' column
test_002['S_Signal_V_010'] = np.nan  # Use NaN instead of empty string for consistency


# Iterate through the DataFrame to set S_Signal_V_010
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['B_Flag_009'] == 'B' and row['Sell_F_010'] > 0:
        current_flag_3 = row['Signal_V_010']
    elif pd.isna(row['B_Flag_009']):
        current_flag_3 = np.nan
    test_002.at[index, 'S_Signal_V_010'] = current_flag_3

# Initialize 'B_Signal_V_010' column
test_002['B_Signal_V_010'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_010
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_002.iterrows():
    if row['S_Flag_009'] == 'S' and row['Buy_F_010'] > 0:
        current_flag_3 = row['Signal_V_010']
    elif pd.isna(row['S_Flag_009']):
        current_flag_3 = np.nan
    test_002.at[index, 'B_Signal_V_010'] = current_flag_3

# Drop the 'Signal_V_010' column
test_002.drop('Signal_V_010', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_010 column without raising warnings
test_002.loc[:, 'P_PCT_010'] = 0.0  # Initialize safely

for i in range(1, len(test_002)):
    # Check for the condition related to B_Flag_010
    if test_002.iloc[i, test_002.columns.get_loc('B_Flag_010')] == 'B' and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_010')] != 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = 0
        
    # Check for the condition related to S_Flag_010
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_010')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_010')] != 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = 0
        
    # Existing logic for calculating P_PCT_010 for B and S flags
    elif test_002.iloc[i, test_002.columns.get_loc('B_Flag_010')] == 'B'  and test_002.iloc[i-1, test_002.columns.get_loc('B_Flag_010')] == 'B':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = round(
            ((test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')] - 
              test_002.iloc[i, test_002.columns.get_loc('B_Signal_V_010')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_002.iloc[i, test_002.columns.get_loc('S_Flag_010')] == 'S' and test_002.iloc[i-1, test_002.columns.get_loc('S_Flag_010')] == 'S':
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = round(
            ((test_002.iloc[i, test_002.columns.get_loc('S_Signal_V_010')] - 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) / 
              test_002.iloc[i-1, test_002.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('S_Flag_010')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = np.nan
        
    elif pd.isna(test_002.iloc[i, test_002.columns.get_loc('B_Flag_010')]):
        test_002.iloc[i, test_002.columns.get_loc('P_PCT_010')] = np.nan



#COMbINATION IS Remain

# Ensure columns exist with appropriate dtype (float)
test_002['Sell_S_P_011'] = np.nan
test_002['Buy_S_P_011'] = np.nan
test_002['Sell_F_011'] = 0.0  # Explicitly set to float
test_002['Buy_F_011'] = 0.0   # Explicitly set to float

triggered_short_011 = False
triggered_long_011 = False
prev_flag_011 = None  # Now will store (S_Flag_009, B_Flag_009)

# Remove whitespace from column names
test_002.columns = test_002.columns.str.strip()

# Ensure required columns exist
required_cols = ["S_Flag_009", "B_Flag_009", "B_Flag_010", 
                 "P_PCT_010", "S_Flag_010", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_002.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx, row in test_002.iterrows():
    try:
        # Reset flags when flag combination changes
        current_flag_state = (row["S_Flag_009"], row["B_Flag_009"])
        if current_flag_state != prev_flag_011:
            triggered_short_011 = False
            triggered_long_011 = False

        # Process "S" flag condition
        if row["S_Flag_009"] == "S":
            pct_value = pd.to_numeric(row["P_PCT_010"], errors="coerce")
            if row["B_Flag_010"] == "B" and pct_value < -1.5:
                if not triggered_short_011:
                    Sell_S_P_011 = float(row["CLOSE"])  # Explicitly float
                    test_002.at[idx, "Sell_S_P_011"] = Sell_S_P_011
                    test_002.at[idx, "Sell_F_011"] = Sell_S_P_011
                    triggered_short_011 = True

        # Process "B" flag condition
        if row["B_Flag_009"] == "B":
            pct_value = pd.to_numeric(row["P_PCT_010"], errors="coerce")
            if row["S_Flag_010"] == "S" and pct_value < -1.5:
                if not triggered_long_011:
                    Buy_S_P_011 = float(row["CLOSE"])
                    test_002.at[idx, "Buy_S_P_011"] = Buy_S_P_011
                    test_002.at[idx, "Buy_F_011"] = Buy_S_P_011
                    triggered_long_011 = True

        prev_flag_011 = current_flag_state

    except Exception as e:
        print(f"Error at row {idx}: {e}")





# Calculate combined signals with error handling
try:
    test_002['Combined_Buy_03'] = test_002[['Buy_F_007', 'Buy_F_008', 'Buy_F_009', 'Buy_F_010', 'Buy_F_011']].fillna(0).sum(axis=1)
    test_002['Combined_Sell_03'] = test_002[['Sell_F_007', 'Sell_F_008', 'Sell_F_009', 'Sell_F_010', 'Sell_F_011']].fillna(0).sum(axis=1)
except Exception as e:
    #print(f"Error creating combined columns: {e}")
    print(f"{formatted_time} - Error creating combined columns: {e}")  
    raise


# Initialize lists for signals
BUY_012 = []
SELL_012 = []
b_list_012 = []
s_list_012 = []

# Initialize position flag
position_012 = False

# Process signals
try:
    for i in range(len(test_002)):
        current_row = test_002.iloc[i]
        
        if not position_012:
            if current_row['Combined_Buy_03'] > 0 and current_row['Combined_Sell_03'] == 0:
                BUY_012.append(current_row['CLOSE'])
                b_list_012.append(current_row['CLOSE'])
                SELL_012.append(np.nan)
                s_list_012.append(0)
                position_012 = True
            else:
                BUY_012.append(np.nan)
                b_list_012.append(0)
                SELL_012.append(np.nan)
                s_list_012.append(0)
        else:
            if current_row['Combined_Sell_03'] > 0 and current_row['Combined_Buy_03'] == 0:
                BUY_012.append(np.nan)
                b_list_012.append(0)
                SELL_012.append(current_row['CLOSE'])
                s_list_012.append(current_row['CLOSE'])
                position_012 = False
            else:
                BUY_012.append(np.nan)
                b_list_012.append(0)
                SELL_012.append(np.nan)
                s_list_012.append(0)

    # Add signals to DataFrame with error handling
    test_002['Buy_S_P_012'] = BUY_012
    test_002['Sell_S_P_012'] = SELL_012
    test_002['Buy_F_012'] = b_list_012
    test_002['Sell_F_012'] = s_list_012

    # Verify columns were created
    new_columns = ['Buy_S_P_012', 'Sell_S_P_012', 'Buy_F_012', 'Sell_F_012']
    for col in new_columns:
        if col not in test_002.columns:
            print(f"{formatted_time} - Warning: Column {col} was not created")            
            
except Exception as e:
    print(str(formatted_time) + ' - ' + "Error during signal processing ...")
    raise


test_003 = test_002.copy()

# Replace 0 with NaN in the 'Sell_F_012' column and assign it to the 'Signal_V_012' column
test_003['Signal_V_012'] = test_003['Sell_F_012'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_012' column with corresponding 'Buy_F_012' values by replacing 0 with NaN, and apply forward-fill
test_003['Signal_V_012'] = test_003['Signal_V_012'].fillna(test_003['Buy_F_012'].replace(0, np.nan)).ffill()

# Initialize 'Flag_012' column with NaN values and ensure it's of type 'object' (string)
test_003['Flag_012'] = np.nan  # Initialize with NaN
test_003['Flag_012'] = test_003['Flag_012'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_012 and 'S' for Sell_F_012 in the 'Flag_012' column
test_003.loc[test_003['Buy_F_012'] != 0, 'Flag_012'] = 'B'
test_003.loc[test_003['Sell_F_012'] != 0, 'Flag_012'] = 'S'

# Forward-fill the NaN values in the 'Flag_012' column
test_003['Flag_012'] = test_003['Flag_012'].ffill()

# Initialize 'P_PCT_012' column with 0
test_003['P_PCT_012'] = 0.0

# Signal PCT
# Initialize the P_PCT_012 column without raising warnings
test_003.loc[:, 'P_PCT_012'] = 0.0  # Initialize safely

for i in range(1, len(test_003)):
    # Check for the condition related to B_Flag_012
    if test_003.iloc[i, test_003.columns.get_loc('Flag_012')] == 'B' and test_003.iloc[i-1, test_003.columns.get_loc('Flag_012')] != 'B':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = 0
        
    # Check for the condition related to S_Flag_012
    elif test_003.iloc[i, test_003.columns.get_loc('Flag_012')] == 'S' and test_003.iloc[i-1, test_003.columns.get_loc('Flag_012')] != 'S':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = 0
        
    # Existing logic for calculating P_PCT_012 for B and S flags
    elif test_003.iloc[i, test_003.columns.get_loc('Flag_012')] == 'B'  and test_003.iloc[i-1, test_003.columns.get_loc('Flag_012')] == 'B':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = round(
            ((test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')] - 
              test_003.iloc[i, test_003.columns.get_loc('Signal_V_012')]) / 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_003.iloc[i, test_003.columns.get_loc('Flag_012')] == 'S' and test_003.iloc[i-1, test_003.columns.get_loc('Flag_012')] == 'S':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = round(
            ((test_003.iloc[i, test_003.columns.get_loc('Signal_V_012')] - 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) / 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_003.iloc[i, test_003.columns.get_loc('Flag_012')]):
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = np.nan
        
    elif pd.isna(test_003.iloc[i, test_003.columns.get_loc('Flag_012')]):
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_012')] = np.nan


# Initialize columns for long and short min/max values
test_003['Long_Min_012'] = np.nan
test_003['Long_Max_012'] = np.nan
test_003['Short_Min_012'] = np.nan
test_003['Short_Max_012'] = np.nan

# Track min and max values for long and short positions
cur_long_min_012 = None
cur_short_min_012 = None
cur_long_max_012 = None
cur_short_max_012 = None

for idx, row in test_003.iterrows():
    if row['Flag_012'] == 'B':
        if cur_long_min_012 is None or row['P_PCT_012'] < cur_long_min_012:
            cur_long_min_012 = row['P_PCT_012']
        if cur_long_max_012 is None or row['P_PCT_012'] > cur_long_max_012:
            cur_long_max_012 = row['P_PCT_012']
        test_003.at[idx, 'Long_Max_012'] = cur_long_max_012
        test_003.at[idx, 'Long_Min_012'] = cur_long_min_012
        cur_short_min_012 = None
        cur_short_max_012 = None
    elif row['Flag_012'] == 'S':
        if cur_short_min_012 is None or row['P_PCT_012'] < cur_short_min_012:
            cur_short_min_012 = row['P_PCT_012']
        if cur_short_max_012 is None or row['P_PCT_012'] > cur_short_max_012:
            cur_short_max_012 = row['P_PCT_012']
        test_003.at[idx, 'Short_Max_012'] = cur_short_max_012
        test_003.at[idx, 'Short_Min_012'] = cur_short_min_012
        cur_long_min_012 = None
        cur_long_max_012 = None


# Initialize columns for percentage calculations
test_003['LONG_PCT_012'] = np.nan
test_003['SHORT_PCT_012'] = np.nan

# Initialize 'LONG_PCT_012' and 'SHORT_PCT_012' columns with NaN values and ensure they're of type 'object'
test_003['LONG_PCT_012'] = np.nan  # Initialize with NaN
test_003['LONG_PCT_012'] = test_003['LONG_PCT_012'].astype('object')  # Convert to string type
test_003['SHORT_PCT_012'] = np.nan  # Initialize with NaN
test_003['SHORT_PCT_012'] = test_003['SHORT_PCT_012'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index, row in test_003.iterrows():
    if pd.notna(row['Long_Max_012']):
        if row['Long_Max_012'] != 0:
            long_pct_012 = (row['Long_Max_012'] - row['P_PCT_012']) / row['Long_Max_012'] * 100
        else:
            long_pct_012 = 0
        test_003.at[index, 'LONG_PCT_012'] = f"{long_pct_012:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_012']):
        if row['Short_Max_012'] != 0:
            short_pct_012 = (row['Short_Max_012'] - row['P_PCT_012']) / row['Short_Max_012'] * 100
        else:
            short_pct_012 = 0
        test_003.at[index, 'SHORT_PCT_012'] = f"{short_pct_012:.2f}"  # Store as formatted string


# Initialize columns
test_003['Sell_S_P_013'] = None
test_003['Buy_S_P_013'] = None
test_003['Sell_F_013'] = 0.0
test_003['Buy_F_013'] = 0.0

# Clean column names
test_003.columns = test_003.columns.str.strip()

# Initialize flags
triggered_short = False
triggered_long = False
prev_flag = None

for idx, row in test_003.iterrows():
    try:
        # Verify required columns exist
        required_cols = ['Flag_012', 'Short_Max_012', 'Long_Max_012', 
                        'SHORT_PCT_012', 'LONG_PCT_012', 'CLOSE']
        missing_cols = [col for col in required_cols if col not in test_003.columns]
        if missing_cols:
            raise KeyError(f"Missing columns: {missing_cols}")

        # Get previous row if exists
        prev_row = test_003.loc[test_003.index[test_003.index.get_loc(idx)-1]] if idx > test_003.index[0] else None

        # Reset triggers when flag changes
        if prev_flag is not None and row['Flag_012'] != prev_flag:
            triggered_short = False
            triggered_long = False

        # SELL LOGIC (Triggered on B flag)
        if row['Flag_012'] == 'B' and (prev_row['Flag_012'] == 'B') and not triggered_long:
            long_max = float(row['Long_Max_012'])
            long_pct = float(row['LONG_PCT_012'])
            p_pct = float(row['P_PCT_012'])
            
            # Define sell conditions
            sell_conditions = [             
                (5 < long_max <= 7) and (long_pct >= 90),
                (7 < long_max <= 9) and (long_pct >= 65),
                (9 < long_max <= 12) and (long_pct >= 58),
                (12 < long_max <= 16) and (long_pct >= 55), 
                (16 < long_max <= 22) and (long_pct >= 50),
                (22 < long_max <= 25) and (long_pct >= 45),
                (25 < long_max <= 27.5) and (long_pct >= 52),
                (27.5 < long_max <= 30) and (long_pct >= 42),
                (30 < long_max <= 35) and (long_pct >= 40),
                (35 < long_max <= 40) and (long_pct >= 30),                
                (40 < long_max <= 50) and (long_pct >= 20), 
                (50 < long_max <= 60) and (long_pct >= 19),
                (60 < long_max <= 70) and (long_pct >= 16),                
                (long_max > 70) and (long_pct >= 10),
                (p_pct < -2)
            ]
            
            # Trigger sell if any condition met
            if any(sell_conditions):
                sell_price = row['CLOSE']
                test_003.at[idx, 'Sell_S_P_013'] = sell_price
                test_003.at[idx, 'Sell_F_013'] = sell_price
                triggered_long = True

        # BUY LOGIC (Triggered on S flag)
        elif row['Flag_012'] == 'S' and (prev_row['Flag_012'] == 'S') and not triggered_short:
            short_max = float(row['Short_Max_012'])
            short_pct = float(row['SHORT_PCT_012'])
            p_pct = float(row['P_PCT_012'])
            
            # Define buy conditions
            buy_conditions = [              
                (5 < short_max <= 7) and (short_pct >= 90),
                (7 < short_max <= 9) and (short_pct >= 65),
                (9 < short_max <= 12) and (short_pct >= 58),
                (12 < short_max <= 16) and (short_pct >= 55), 
                (16 < short_max <= 22) and (short_pct >= 50),
                (22 < short_max <= 25) and (short_pct >= 45),
                (25 < short_max <= 27.5) and (short_pct >= 52),
                (27.5 < short_max <= 30) and (short_pct >= 42),
                (30 < short_max <= 35) and (short_pct >= 40),
                (35 < short_max <= 40) and (short_pct >= 30),                
                (40 < short_max <= 50) and (short_pct >= 20), 
                (50 < short_max <= 60) and (short_pct >= 19),                 
                (60 < short_max <= 70) and (short_pct >= 16),
                (short_max > 70) and (short_pct >= 10), 
                (p_pct < -2)
            ]
            
            # Trigger buy if any condition met
            if any(buy_conditions):
                buy_price = row['CLOSE']
                test_003.at[idx, 'Buy_S_P_013'] = buy_price
                test_003.at[idx, 'Buy_F_013'] = buy_price
                triggered_short = True

        # Update previous flag
        prev_flag = row['Flag_012']

    except KeyError as e:
        print(f"KeyError at index {idx}: {e}")
    except ValueError as e:
        print(f"ValueError at index {idx}: {e}")
    except Exception as e:
        print(f"Unexpected error at index {idx}: {e}")



test_003['S_Flag_013'] = ''
test_003['B_Flag_013'] = ''

# Iterate through the DataFrame to set B_Flag_013
current_flag_1 = ''
current_flag_2 = ''
for index, row in test_003.iterrows():
    if row['Flag_012'] == 'B' and row['Sell_F_013'] > 0:
        current_flag_1 = 'S'
    elif row['Flag_012'] == 'S':
        current_flag_1 = np.nan
    test_003.at[index, 'S_Flag_013'] = current_flag_1
    
# Iterate through the DataFrame to set S_Flag_013
current_flag_3 = ''
current_flag_4 = ''
for index, row in test_003.iterrows():
    if row['Flag_012'] == 'S' and row['Buy_F_013'] > 0:
        current_flag_3 = 'B'        
    elif row['Flag_012'] == 'B':
        current_flag_3 = np.nan
    test_003.at[index, 'B_Flag_013'] = current_flag_3

# Replace 0 with NaN in the 'Sell_F_013' column and assign it to the 'Signal_V_013' column
test_003['Signal_V_013'] = test_003['Sell_F_013'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_013' column with corresponding 'Buy_F_013' values by replacing 0 with NaN
test_003['Signal_V_013'] = test_003['Signal_V_013'].fillna(test_003['Buy_F_013'].replace(0, np.nan))

# Apply forward-fill to fill remaining NaN values
test_003['Signal_V_013'] = test_003['Signal_V_013'].ffill()

# Initialize 'S_Signal_V_013' column
test_003['S_Signal_V_013'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set S_Signal_V_013
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_003.iterrows():
    if row['Flag_012'] == 'B' and row['Sell_F_013'] > 0:
        current_flag_3 = row['Signal_V_013']
    elif row['Flag_012'] == 'S':
        current_flag_3 = np.nan
    test_003.at[index, 'S_Signal_V_013'] = current_flag_3

# Initialize 'B_Signal_V_013' column
test_003['B_Signal_V_013'] = np.nan  # Use NaN instead of empty string for consistency

# Iterate through the DataFrame to set B_Signal_V_013
current_flag_3 = np.nan  # Initialize as NaN
for index, row in test_003.iterrows():
    if row['Flag_012'] == 'S' and row['Buy_F_013'] > 0:
        current_flag_3 = row['Signal_V_013']
    elif row['Flag_012'] == 'B':
        current_flag_3 = np.nan
    test_003.at[index, 'B_Signal_V_013'] = current_flag_3

# Drop the 'Signal_V_013' column
test_003.drop('Signal_V_013', axis=1, inplace=True)


# SL PCT
# Initialize the P_PCT_013 column without raising warnings
test_003.loc[:, 'P_PCT_013'] = 0.0  # Initialize safely

for i in range(1, len(test_003)):
    # Check for the condition related to B_Flag_013
    if test_003.iloc[i, test_003.columns.get_loc('B_Flag_013')] == 'B' and test_003.iloc[i-1, test_003.columns.get_loc('B_Flag_013')] != 'B':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = 0
        
    # Check for the condition related to S_Flag_013
    elif test_003.iloc[i, test_003.columns.get_loc('S_Flag_013')] == 'S' and test_003.iloc[i-1, test_003.columns.get_loc('S_Flag_013')] != 'S':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = 0
        
    # Existing logic for calculating P_PCT_013 for B and S flags
    elif test_003.iloc[i, test_003.columns.get_loc('B_Flag_013')] == 'B'  and test_003.iloc[i-1, test_003.columns.get_loc('B_Flag_013')] == 'B':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = round(
            ((test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')] - 
              test_003.iloc[i, test_003.columns.get_loc('B_Signal_V_013')]) / 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_003.iloc[i, test_003.columns.get_loc('S_Flag_013')] == 'S' and test_003.iloc[i-1, test_003.columns.get_loc('S_Flag_013')] == 'S':
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = round(
            ((test_003.iloc[i, test_003.columns.get_loc('S_Signal_V_013')] - 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) / 
              test_003.iloc[i-1, test_003.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_003.iloc[i, test_003.columns.get_loc('S_Flag_013')]):
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = np.nan
        
    elif pd.isna(test_003.iloc[i, test_003.columns.get_loc('B_Flag_013')]):
        test_003.iloc[i, test_003.columns.get_loc('P_PCT_013')] = np.nan


#TP SL impmented newly on Sep 2025. 
#---------------------------------------------------------------
# Ensure columns exist with appropriate dtype (float)
test_003['Sell_S_P_014'] = np.nan
test_003['Buy_S_P_014'] = np.nan
test_003['Sell_F_014'] = 0.0  # Explicitly set to float
test_003['Buy_F_014'] = 0.0   # Explicitly set to float

triggered_short_014 = False
triggered_long_014 = False
prev_flag_014 = None

# Remove whitespace from column names
test_003.columns = test_003.columns.str.strip()

# Ensure required columns exist
required_cols = ["Flag_012", "B_Flag_013", "P_PCT_013", "S_Flag_013", "CLOSE"]
missing_cols = [col for col in required_cols if col not in test_003.columns]
if missing_cols:
    raise KeyError(f"Missing columns: {missing_cols}")

# Iterate through the DataFrame
for idx, row in test_003.iterrows():
    try:
        # Reset flags when Flag_012 changes
        if row["Flag_012"] != prev_flag_014:
            triggered_short_014 = False
            triggered_long_014 = False

        # Process "S" flag condition
        if row["Flag_012"] == "S":
            try:
                pct_value = pd.to_numeric(row["P_PCT_013"], errors="coerce")
                if row["B_Flag_013"] == "B" and pct_value < -1.4:
                    if not triggered_short_014:
                        Sell_S_P_014 = float(row["CLOSE"])  # Ensure it's explicitly float
                        test_003.at[idx, "Sell_S_P_014"] = Sell_S_P_014
                        test_003.at[idx, "Sell_F_014"] = Sell_S_P_014  # No warning now
                        triggered_short_014 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'S' flag: {e}")

        # Process "B" flag condition
        elif row["Flag_012"] == "B":
            try:
                pct_value = pd.to_numeric(row["P_PCT_013"], errors="coerce")
                if row["S_Flag_013"] == "S" and pct_value < -1.4:
                    if not triggered_long_014:
                        Buy_S_P_014 = float(row['CLOSE'])  # Ensure it's explicitly float
                        test_003.at[idx, 'Buy_S_P_014'] = Buy_S_P_014
                        test_003.at[idx, 'Buy_F_014'] = Buy_S_P_014  # No warning now
                        triggered_long_014 = True
            except Exception as e:
                print(f"Error processing row index {idx} for 'B' flag: {e}")

        prev_flag_014 = row["Flag_012"]

    except KeyError as e:
        print(f"KeyError: {e} for row index {idx}. Available columns: {test_003.columns.tolist()}")
    except ValueError as e:
        print(f"ValueError: {e} in row index {idx}, data: {row}")
    except Exception as e:
        print(f"Unexpected error for row index {idx}: {e}")



# Calculate combined signals with error handling
try:
    test_003['Combined_Buy_04'] = test_003[['Buy_F_012', 'Buy_F_013', 'Buy_F_014']].fillna(0).sum(axis=1)
    test_003['Combined_Sell_04'] = test_003[['Sell_F_012', 'Sell_F_013', 'Sell_F_014']].fillna(0).sum(axis=1)
except Exception as e:
    #print(f"Error creating combined columns: {e}")
    print(f"{formatted_time} - Error creating combined columns: {e}")  
    raise


# Initialize lists for signals
BUY_015 = []
SELL_015 = []
b_list_015 = []
s_list_015 = []

# Initialize position flag
position_015 = False

# Process signals
try:
    for i in range(len(test_003)):
        current_row = test_003.iloc[i]
        
        if not position_015:
            if current_row['Combined_Buy_04'] > 0 and current_row['Combined_Sell_04'] == 0:
                BUY_015.append(current_row['CLOSE'])
                b_list_015.append(current_row['CLOSE'])
                SELL_015.append(np.nan)
                s_list_015.append(0)
                position_015 = True
            else:
                BUY_015.append(np.nan)
                b_list_015.append(0)
                SELL_015.append(np.nan)
                s_list_015.append(0)
        else:
            if current_row['Combined_Sell_04'] > 0 and current_row['Combined_Buy_04'] == 0:
                BUY_015.append(np.nan)
                b_list_015.append(0)
                SELL_015.append(current_row['CLOSE'])
                s_list_015.append(current_row['CLOSE'])
                position_015 = False
            else:
                BUY_015.append(np.nan)
                b_list_015.append(0)
                SELL_015.append(np.nan)
                s_list_015.append(0)

    # Add signals to DataFrame with error handling
    test_003['Buy_S_P_015'] = BUY_015
    test_003['Sell_S_P_015'] = SELL_015
    test_003['Buy_F_015'] = b_list_015
    test_003['Sell_F_015'] = s_list_015

    # Verify columns were created
    new_columns = ['Buy_S_P_015', 'Sell_S_P_015', 'Buy_F_015', 'Sell_F_015']
    for col in new_columns:
        if col not in test_003.columns:
            print(f"{formatted_time} - Warning: Column {col} was not created")            
            
except Exception as e:
    print(str(formatted_time) + ' - ' + "Error during signal processing ...")
    raise



test_004 = test_003.copy()

# Replace 0 with NaN in the 'Sell_F_015' column and assign it to the 'Signal_V_015' column
test_004['Signal_V_015'] = test_004['Sell_F_015'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_015' column with corresponding 'Buy_F_015' values by replacing 0 with NaN, and apply forward-fill
test_004['Signal_V_015'] = test_004['Signal_V_015'].fillna(test_004['Buy_F_015'].replace(0, np.nan)).ffill()

# Initialize 'Flag_015' column with NaN values and ensure it's of type 'object' (string)
test_004['Flag_015'] = np.nan  # Initialize with NaN
test_004['Flag_015'] = test_004['Flag_015'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_015 and 'S' for Sell_F_015 in the 'Flag_015' column
test_004.loc[test_004['Buy_F_015'] != 0, 'Flag_015'] = 'B'
test_004.loc[test_004['Sell_F_015'] != 0, 'Flag_015'] = 'S'

# Forward-fill the NaN values in the 'Flag_015' column
test_004['Flag_015'] = test_004['Flag_015'].ffill()

# Initialize 'P_PCT_015' column with 0
test_004['P_PCT_015'] = 0.0

# Signal PCT
# Initialize the P_PCT_015 column without raising warnings
test_004.loc[:, 'P_PCT_015'] = 0.0  # Initialize safely

for i in range(1, len(test_004)):
    # Check for the condition related to B_Flag_015
    if test_004.iloc[i, test_004.columns.get_loc('Flag_015')] == 'B' and test_004.iloc[i-1, test_004.columns.get_loc('Flag_015')] != 'B':
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = 0
        
    # Check for the condition related to S_Flag_015
    elif test_004.iloc[i, test_004.columns.get_loc('Flag_015')] == 'S' and test_004.iloc[i-1, test_004.columns.get_loc('Flag_015')] != 'S':
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = 0
        
    # Existing logic for calculating P_PCT_015 for B and S flags
    elif test_004.iloc[i, test_004.columns.get_loc('Flag_015')] == 'B'  and test_004.iloc[i-1, test_004.columns.get_loc('Flag_015')] == 'B':
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = round(
            ((test_004.iloc[i-1, test_004.columns.get_loc('CLOSE')] - 
              test_004.iloc[i, test_004.columns.get_loc('Signal_V_015')]) / 
              test_004.iloc[i-1, test_004.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_004.iloc[i, test_004.columns.get_loc('Flag_015')] == 'S' and test_004.iloc[i-1, test_004.columns.get_loc('Flag_015')] == 'S':
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = round(
            ((test_004.iloc[i, test_004.columns.get_loc('Signal_V_015')] - 
              test_004.iloc[i-1, test_004.columns.get_loc('CLOSE')]) / 
              test_004.iloc[i-1, test_004.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_004.iloc[i, test_004.columns.get_loc('Flag_015')]):
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = np.nan
        
    elif pd.isna(test_004.iloc[i, test_004.columns.get_loc('Flag_015')]):
        test_004.iloc[i, test_004.columns.get_loc('P_PCT_015')] = np.nan


# Initialize columns for long and short min/max values
test_004['Long_Min_015'] = np.nan
test_004['Long_Max_015'] = np.nan
test_004['Short_Min_015'] = np.nan
test_004['Short_Max_015'] = np.nan

# Track min and max values for long and short positions
cur_long_min_015 = None
cur_short_min_015 = None
cur_long_max_015 = None
cur_short_max_015 = None

for idx, row in test_004.iterrows():
    if row['Flag_015'] == 'B':
        if cur_long_min_015 is None or row['P_PCT_015'] < cur_long_min_015:
            cur_long_min_015 = row['P_PCT_015']
        if cur_long_max_015 is None or row['P_PCT_015'] > cur_long_max_015:
            cur_long_max_015 = row['P_PCT_015']
        test_004.at[idx, 'Long_Max_015'] = cur_long_max_015
        test_004.at[idx, 'Long_Min_015'] = cur_long_min_015
        cur_short_min_015 = None
        cur_short_max_015 = None
    elif row['Flag_015'] == 'S':
        if cur_short_min_015 is None or row['P_PCT_015'] < cur_short_min_015:
            cur_short_min_015 = row['P_PCT_015']
        if cur_short_max_015 is None or row['P_PCT_015'] > cur_short_max_015:
            cur_short_max_015 = row['P_PCT_015']
        test_004.at[idx, 'Short_Max_015'] = cur_short_max_015
        test_004.at[idx, 'Short_Min_015'] = cur_short_min_015
        cur_long_min_015 = None
        cur_long_max_015 = None


# Initialize columns for percentage calculations
test_004['LONG_PCT_015'] = np.nan
test_004['SHORT_PCT_015'] = np.nan

# Initialize 'LONG_PCT_015' and 'SHORT_PCT_015' columns with NaN values and ensure they're of type 'object'
test_004['LONG_PCT_015'] = np.nan  # Initialize with NaN
test_004['LONG_PCT_015'] = test_004['LONG_PCT_015'].astype('object')  # Convert to string type
test_004['SHORT_PCT_015'] = np.nan  # Initialize with NaN
test_004['SHORT_PCT_015'] = test_004['SHORT_PCT_015'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index, row in test_004.iterrows():
    if pd.notna(row['Long_Max_015']):
        if row['Long_Max_015'] != 0:
            long_pct_015 = (row['Long_Max_015'] - row['P_PCT_015']) / row['Long_Max_015'] * 100
        else:
            long_pct_015 = 0
        test_004.at[index, 'LONG_PCT_015'] = f"{long_pct_015:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_015']):
        if row['Short_Max_015'] != 0:
            short_pct_015 = (row['Short_Max_015'] - row['P_PCT_015']) / row['Short_Max_015'] * 100
        else:
            short_pct_015 = 0
        test_004.at[index, 'SHORT_PCT_015'] = f"{short_pct_015:.2f}"  # Store as formatted string


# Initialize columns
test_004['Sell_S_P_016'] = None
test_004['Buy_S_P_016'] = None
test_004['Sell_F_016'] = 0.0
test_004['Buy_F_016'] = 0.0

# Clean column names
test_004.columns = test_004.columns.str.strip()

# Initialize flags
triggered_short = False
triggered_long = False
prev_flag = None

for idx, row in test_004.iterrows():
    try:
        # Verify required columns exist
        required_cols = ['Flag_015', 'Short_Max_015', 'Long_Max_015', 
                        'SHORT_PCT_015', 'LONG_PCT_015', 'CLOSE']
        missing_cols = [col for col in required_cols if col not in test_004.columns]
        if missing_cols:
            raise KeyError(f"Missing columns: {missing_cols}")

        # Get previous row if exists
        prev_row = test_004.loc[test_004.index[test_004.index.get_loc(idx)-1]] if idx > test_004.index[0] else None

        # Reset triggers when flag changes
        if prev_flag is not None and row['Flag_015'] != prev_flag:
            triggered_short = False
            triggered_long = False

        # SELL LOGIC (Triggered on B flag)
        if row['Flag_015'] == 'B' and (prev_row['Flag_015'] == 'B') and not triggered_long:
            long_max = float(row['Long_Max_015'])
            long_pct = float(row['LONG_PCT_015'])
            p_pct = float(row['P_PCT_015'])
            
            # Define sell conditions
            sell_conditions = [             
                (7 < long_max <= 9) and (long_pct >= 65),
                (9 < long_max <= 12) and (long_pct >= 58),
                (12 < long_max <= 16) and (long_pct >= 55), 
                (16 < long_max <= 22) and (long_pct >= 50),
                (22 < long_max <= 25) and (long_pct >= 45),
                (25 < long_max <= 27.5) and (long_pct >= 52),
                (27.5 < long_max <= 30) and (long_pct >= 42),
                (30 < long_max <= 35) and (long_pct >= 40),
                (35 < long_max <= 40) and (long_pct >= 30),                
                (40 < long_max <= 50) and (long_pct >= 25), 
                (50 < long_max <= 60) and (long_pct >= 19),
                (60 < long_max <= 70) and (long_pct >= 16),                
                (long_max > 70) and (long_pct >= 10),
                (p_pct < -2.8)
            ]
            
            # Trigger sell if any condition met
            if any(sell_conditions):
                sell_price = row['CLOSE']
                test_004.at[idx, 'Sell_S_P_016'] = sell_price
                test_004.at[idx, 'Sell_F_016'] = sell_price
                triggered_long = True

        # BUY LOGIC (Triggered on S flag)
        elif row['Flag_015'] == 'S' and (prev_row['Flag_015'] == 'S') and not triggered_short:
            short_max = float(row['Short_Max_015'])
            short_pct = float(row['SHORT_PCT_015'])
            p_pct = float(row['P_PCT_015'])
            
            # Define buy conditions
            buy_conditions = [              
                (7 < short_max <= 9) and (short_pct >= 65),
                (9 < short_max <= 12) and (short_pct >= 58),
                (12 < short_max <= 16) and (short_pct >= 55), 
                (16 < short_max <= 22) and (short_pct >= 50),
                (22 < short_max <= 25) and (short_pct >= 45),
                (25 < short_max <= 27.5) and (short_pct >= 52),
                (27.5 < short_max <= 30) and (short_pct >= 42),
                (30 < short_max <= 35) and (short_pct >= 40),
                (35 < short_max <= 40) and (short_pct >= 30),                
                (40 < short_max <= 50) and (short_pct >= 25), 
                (50 < short_max <= 60) and (short_pct >= 19),                 
                (60 < short_max <= 70) and (short_pct >= 16),
                (short_max > 70) and (short_pct >= 10), 
                (p_pct < -2.8)
            ]
            
            # Trigger buy if any condition met
            if any(buy_conditions):
                buy_price = row['CLOSE']
                test_004.at[idx, 'Buy_S_P_016'] = buy_price
                test_004.at[idx, 'Buy_F_016'] = buy_price
                triggered_short = True

        # Update previous flag
        prev_flag = row['Flag_015']

    except KeyError as e:
        print(f"KeyError at index {idx}: {e}")
    except ValueError as e:
        print(f"ValueError at index {idx}: {e}")
    except Exception as e:
        print(f"Unexpected error at index {idx}: {e}")




# Calculate combined signals with error handling
try:
    test_004['Combined_Buy_05'] = test_004[['Buy_F_016', 'Buy_F_015']].fillna(0).sum(axis=1)
    test_004['Combined_Sell_05'] = test_004[['Sell_F_016', 'Sell_F_015']].fillna(0).sum(axis=1)
except Exception as e:
    #print(f"Error creating combined columns: {e}")
    print(f"{formatted_time} - Error creating combined columns: {e}")  
    raise


# Initialize lists for signals
BUY_017 = []
SELL_017 = []
b_list_017 = []
s_list_017 = []

# Initialize position flag
position_017 = False

# Process signals
try:
    for i in range(len(test_004)):
        current_row = test_004.iloc[i]
        
        if not position_017:
            if current_row['Combined_Buy_05'] > 0 and current_row['Combined_Sell_05'] == 0:
                BUY_017.append(current_row['CLOSE'])
                b_list_017.append(current_row['CLOSE'])
                SELL_017.append(np.nan)
                s_list_017.append(0)
                position_017 = True
            else:
                BUY_017.append(np.nan)
                b_list_017.append(0)
                SELL_017.append(np.nan)
                s_list_017.append(0)
        else:
            if current_row['Combined_Sell_05'] > 0 and current_row['Combined_Buy_05'] == 0:
                BUY_017.append(np.nan)
                b_list_017.append(0)
                SELL_017.append(current_row['CLOSE'])
                s_list_017.append(current_row['CLOSE'])
                position_017 = False
            else:
                BUY_017.append(np.nan)
                b_list_017.append(0)
                SELL_017.append(np.nan)
                s_list_017.append(0)

    # Add signals to DataFrame with error handling
    test_004['Buy_S_P_017'] = BUY_017
    test_004['Sell_S_P_017'] = SELL_017
    test_004['Buy_F_017'] = b_list_017
    test_004['Sell_F_017'] = s_list_017

    # Verify columns were created
    new_columns = ['Buy_S_P_017', 'Sell_S_P_017', 'Buy_F_017', 'Sell_F_017']
    for col in new_columns:
        if col not in test_004.columns:
            print(f"{formatted_time} - Warning: Column {col} was not created")            
            
except Exception as e:
    print(str(formatted_time) + ' - ' + "Error during signal processing ...")
    raise


test_005 = test_004.copy()

# Replace 0 with NaN in the 'Sell_F_017' column and assign it to the 'Signal_V_017' column
test_005['Signal_V_017'] = test_005['Sell_F_017'].replace(0, np.nan)

# Fill NaN values in the 'Signal_V_017' column with corresponding 'Buy_F_017' values by replacing 0 with NaN, and apply forward-fill
test_005['Signal_V_017'] = test_005['Signal_V_017'].fillna(test_005['Buy_F_017'].replace(0, np.nan)).ffill()

# Initialize 'Flag_017' column with NaN values and ensure it's of type 'object' (string)
test_005['Flag_017'] = np.nan  # Initialize with NaN
test_005['Flag_017'] = test_005['Flag_017'].astype('object')  # Convert to string type

# Assign 'B' for Buy_F_017 and 'S' for Sell_F_017 in the 'Flag_017' column
test_005.loc[test_005['Buy_F_017'] != 0, 'Flag_017'] = 'B'
test_005.loc[test_005['Sell_F_017'] != 0, 'Flag_017'] = 'S'

# Forward-fill the NaN values in the 'Flag_017' column
test_005['Flag_017'] = test_005['Flag_017'].ffill()

# Initialize 'P_PCT_017' column with 0
test_005['P_PCT_017'] = 0.0

# Signal PCT
# Initialize the P_PCT_017 column without raising warnings
test_005.loc[:, 'P_PCT_017'] = 0.0  # Initialize safely

for i in range(1, len(test_005)):
    # Check for the condition related to B_Flag_017
    if test_005.iloc[i, test_005.columns.get_loc('Flag_017')] == 'B' and test_005.iloc[i-1, test_005.columns.get_loc('Flag_017')] != 'B':
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = 0
        
    # Check for the condition related to S_Flag_017
    elif test_005.iloc[i, test_005.columns.get_loc('Flag_017')] == 'S' and test_005.iloc[i-1, test_005.columns.get_loc('Flag_017')] != 'S':
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = 0
        
    # Existing logic for calculating P_PCT_017 for B and S flags
    elif test_005.iloc[i, test_005.columns.get_loc('Flag_017')] == 'B'  and test_005.iloc[i-1, test_005.columns.get_loc('Flag_017')] == 'B':
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = round(
            ((test_005.iloc[i-1, test_005.columns.get_loc('CLOSE')] - 
              test_005.iloc[i, test_005.columns.get_loc('Signal_V_017')]) / 
              test_005.iloc[i-1, test_005.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    elif test_005.iloc[i, test_005.columns.get_loc('Flag_017')] == 'S' and test_005.iloc[i-1, test_005.columns.get_loc('Flag_017')] == 'S':
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = round(
            ((test_005.iloc[i, test_005.columns.get_loc('Signal_V_017')] - 
              test_005.iloc[i-1, test_005.columns.get_loc('CLOSE')]) / 
              test_005.iloc[i-1, test_005.columns.get_loc('CLOSE')]) * 100, 2
        )
        
    # Use pd.isna() to check for NaN values
    elif pd.isna(test_005.iloc[i, test_005.columns.get_loc('Flag_017')]):
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = np.nan
        
    elif pd.isna(test_005.iloc[i, test_005.columns.get_loc('Flag_017')]):
        test_005.iloc[i, test_005.columns.get_loc('P_PCT_017')] = np.nan


# Initialize columns for long and short min/max values
test_005['Long_Min_017'] = np.nan
test_005['Long_Max_017'] = np.nan
test_005['Short_Min_017'] = np.nan
test_005['Short_Max_017'] = np.nan

# Track min and max values for long and short positions
cur_long_min_017 = None
cur_short_min_017 = None
cur_long_max_017 = None
cur_short_max_017 = None

for idx, row in test_005.iterrows():
    if row['Flag_017'] == 'B':
        if cur_long_min_017 is None or row['P_PCT_017'] < cur_long_min_017:
            cur_long_min_017 = row['P_PCT_017']
        if cur_long_max_017 is None or row['P_PCT_017'] > cur_long_max_017:
            cur_long_max_017 = row['P_PCT_017']
        test_005.at[idx, 'Long_Max_017'] = cur_long_max_017
        test_005.at[idx, 'Long_Min_017'] = cur_long_min_017
        cur_short_min_017 = None
        cur_short_max_017 = None
    elif row['Flag_017'] == 'S':
        if cur_short_min_017 is None or row['P_PCT_017'] < cur_short_min_017:
            cur_short_min_017 = row['P_PCT_017']
        if cur_short_max_017 is None or row['P_PCT_017'] > cur_short_max_017:
            cur_short_max_017 = row['P_PCT_017']
        test_005.at[idx, 'Short_Max_017'] = cur_short_max_017
        test_005.at[idx, 'Short_Min_017'] = cur_short_min_017
        cur_long_min_017 = None
        cur_long_max_017 = None


# Initialize columns for percentage calculations
test_005['LONG_PCT_017'] = np.nan
test_005['SHORT_PCT_017'] = np.nan

# Initialize 'LONG_PCT_017' and 'SHORT_PCT_017' columns with NaN values and ensure they're of type 'object'
test_005['LONG_PCT_017'] = np.nan  # Initialize with NaN
test_005['LONG_PCT_017'] = test_005['LONG_PCT_017'].astype('object')  # Convert to string type
test_005['SHORT_PCT_017'] = np.nan  # Initialize with NaN
test_005['SHORT_PCT_017'] = test_005['SHORT_PCT_017'].astype('object')  # Convert to string type

# Calculate the max percentage and store as formatted strings
for index, row in test_005.iterrows():
    if pd.notna(row['Long_Max_017']):
        if row['Long_Max_017'] != 0:
            long_pct_017 = (row['Long_Max_017'] - row['P_PCT_017']) / row['Long_Max_017'] * 100
        else:
            long_pct_017 = 0
        test_005.at[index, 'LONG_PCT_017'] = f"{long_pct_017:.2f}"  # Store as formatted string
    if pd.notna(row['Short_Max_017']):
        if row['Short_Max_017'] != 0:
            short_pct_017 = (row['Short_Max_017'] - row['P_PCT_017']) / row['Short_Max_017'] * 100
        else:
            short_pct_017 = 0
        test_005.at[index, 'SHORT_PCT_017'] = f"{short_pct_017:.2f}"  # Store as formatted string

'''

# cleanup logic
BUY_018 = []
SELL_018 = []
b_list_018 = []
s_list_018 = []

position_018 = False

for idx, row in test_005.iterrows():
    i = test_005.index.get_loc(idx)
    
    # Ensure we are not at the first row to prevent index errors
    if i == 0:
        BUY_018.append(np.nan)
        b_list_018.append(0)
        SELL_018.append(np.nan)
        s_list_018.append(0)
        continue
    
    prev_row = test_005.iloc[i - 1]  # Get previous row for comparison

    if not position_018:
        # BUY conditions
        if (
            row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
            and row['P_PCT_017'] > 0
            and row['CLOSE'] > prev_row['CLOSE']
        ):
            BUY_018.append(row['CLOSE'])
            b_list_018.append(row['CLOSE'])
            SELL_018.append(np.nan)
            s_list_018.append(0)
            position_018 = True  # Now we hold a position

        elif (
            row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
            and row['Long_Min_017'] < -1
            and (row['P_PCT_017'] - row['Long_Min_017']) > 1.5
            and row['CLOSE'] > prev_row['CLOSE']
        ):
            BUY_018.append(row['CLOSE'])
            b_list_018.append(row['CLOSE'])
            SELL_018.append(np.nan)
            s_list_018.append(0)
            position_018 = True  # Entering a buy position

        else:
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(np.nan)
            s_list_018.append(0)

    else:  # If position_018 is True (we are holding a position)
        # SELL conditions
        if (
            row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
            and row['P_PCT_017'] > 0
            and row['CLOSE'] < prev_row['CLOSE']
        ):
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(row['CLOSE'])
            s_list_018.append(row['CLOSE'])
            position_018 = False  # Closing the position

        elif (
            row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
            and row['Short_Min_017'] < -1
            and (row['P_PCT_017'] - row['Short_Min_017']) > 1.5
            and row['CLOSE'] < prev_row['CLOSE']
        ):
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(row['CLOSE'])
            s_list_018.append(row['CLOSE'])
            position_018 = False  # Closing the position

        else:
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(np.nan)
            s_list_018.append(0)

# Assign results to DataFrame
test_005['Buy_S_P_018'] = BUY_018
test_005['Sell_S_P_018'] = SELL_018
test_005['Buy_F_018'] = b_list_018
test_005['Sell_F_018'] = s_list_018
'''


# Improved cleanup logic with better signal detection
import numpy as np
import pandas as pd

# Initialize lists for signals
BUY_018 = []
SELL_018 = []
b_list_018 = []
s_list_018 = []

# Position tracking
position_018 = False
last_buy_price = None
last_sell_price = None

# Signal validation parameters
MIN_PRICE_CHANGE_THRESHOLD = 0.0001  # Minimum price change to consider valid
MIN_PCT_THRESHOLD = 0.1  # Minimum percentage threshold for signal validation
LOOKBACK_WINDOW = 3  # Number of periods to look back for signal confirmation

def validate_buy_signal(current_row, prev_row, position_state,
                       min_price_threshold=MIN_PRICE_CHANGE_THRESHOLD,
                       min_pct_threshold=MIN_PCT_THRESHOLD):
    """Enhanced buy signal validation with multiple conditions"""

    # Basic flag condition - must be B flag
    flag_condition = (current_row['Flag_017'] == 'B')

    # More flexible conditions for buy signals
    # Condition 1: Positive P_PCT with any price movement
    pct_positive = (current_row['P_PCT_017'] > 0)

    # Condition 2: Long recovery scenario
    long_recovery = (
        current_row['Long_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Long_Min_017']) > 1.5
    )

    # Condition 3: Strong momentum (high P_PCT regardless of other factors)
    strong_momentum = (current_row['P_PCT_017'] > 1.0)

    # Condition 4: Flag transition from S to B (trend reversal)
    trend_reversal = (prev_row['Flag_017'] == 'S' and current_row['Flag_017'] == 'B')

    # Price validation - allow small negative moves for recovery scenarios
    price_ok = (
        current_row['CLOSE'] >= prev_row['CLOSE'] * 0.995 or  # Allow 0.5% negative move
        long_recovery or
        strong_momentum
    )

    return (
        flag_condition and
        (pct_positive or long_recovery or strong_momentum or trend_reversal) and
        price_ok and
        not position_state
    )

def validate_sell_signal(current_row, prev_row, position_state,
                        min_price_threshold=MIN_PRICE_CHANGE_THRESHOLD,
                        min_pct_threshold=MIN_PCT_THRESHOLD):
    """Enhanced sell signal validation with multiple conditions"""

    # Basic flag condition - must be S flag
    flag_condition = (current_row['Flag_017'] == 'S')

    # More flexible conditions for sell signals
    # Condition 1: Positive P_PCT with any price movement
    pct_positive = (current_row['P_PCT_017'] > 0)

    # Condition 2: Short recovery scenario
    short_recovery = (
        current_row['Short_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Short_Min_017']) > 1.5
    )

    # Condition 3: Strong momentum (high P_PCT regardless of other factors)
    strong_momentum = (current_row['P_PCT_017'] > 1.0)

    # Condition 4: Flag transition from B to S (trend reversal)
    trend_reversal = (prev_row['Flag_017'] == 'B' and current_row['Flag_017'] == 'S')

    # Price validation - allow small positive moves for recovery scenarios
    price_ok = (
        current_row['CLOSE'] <= prev_row['CLOSE'] * 1.005 or  # Allow 0.5% positive move
        short_recovery or
        strong_momentum
    )

    return (
        flag_condition and
        (pct_positive or short_recovery or strong_momentum or trend_reversal) and
        price_ok and
        position_state
    )

# Main processing loop
for idx, row in test_005.iterrows():
    i = test_005.index.get_loc(idx)

    # Handle first row
    if i == 0:
        BUY_018.append(np.nan)
        b_list_018.append(0)
        SELL_018.append(np.nan)
        s_list_018.append(0)
        continue

    prev_row = test_005.iloc[i - 1]

    # Initialize default values
    buy_signal = np.nan
    sell_signal = np.nan
    buy_flag = 0
    sell_flag = 0

    # Check for buy signals when not in position
    if validate_buy_signal(row, prev_row, position_018):
        buy_signal = row['CLOSE']
        buy_flag = row['CLOSE']
        position_018 = True
        last_buy_price = row['CLOSE']

    # Check for sell signals when in position
    elif validate_sell_signal(row, prev_row, position_018):
        sell_signal = row['CLOSE']
        sell_flag = row['CLOSE']
        position_018 = False
        last_sell_price = row['CLOSE']

    # Append signals to lists
    BUY_018.append(buy_signal)
    b_list_018.append(buy_flag)
    SELL_018.append(sell_signal)
    s_list_018.append(sell_flag)

# Assign results to DataFrame
test_005['Buy_S_P_018'] = BUY_018
test_005['Sell_S_P_018'] = SELL_018
test_005['Buy_F_018'] = b_list_018
test_005['Sell_F_018'] = s_list_018

# Print summary statistics
#buy_signals = sum(1 for x in BUY_018 if not pd.isna(x))
#sell_signals = sum(1 for x in SELL_018 if not pd.isna(x))
#print(f"Generated {buy_signals} buy signals and {sell_signals} sell signals")
#print(f"Final position state: {'Long' if position_018 else 'Flat'}")





test_022 = test_005


print(f"{formatted_time} - INFO: Process completed ██████████ 100%")

# Record the end time
end_time = time.time()
# Calculate the time taken in seconds
execution_time = end_time - start_time
# Convert seconds to minutes
execution_time_minutes = execution_time / 60

# Print the results
print( str(formatted_time) + ' - ' + f"Started at: {time.ctime(start_time)}")
print(str(formatted_time) + ' - ' + f"Finished at: {time.ctime(end_time)}")
print(str(formatted_time) + ' - ' + f"Total time taken: {execution_time_minutes:.2f} minutes")


import matplotlib.pyplot as plt
import matplotlib.lines as mlines
import matplotlib.patches as mpatches

# Use all available data for plotting
test_022_plot = test_022.copy()

# Create subplots
fig, (ax1, ax2) = plt.subplots(
    2, 1, figsize=(21, 14), gridspec_kw={'height_ratios': [8, 4]}
)

# Minimal gap between plots
fig.subplots_adjust(hspace=0.08, top=0.95, bottom=0.12, left=0.07, right=0.93)
fig.patch.set_facecolor('white')
ax1.set_facecolor('white')
ax2.set_facecolor('white')

# --------------------
# Price Chart
# --------------------
ax1.set_xlabel('Date', fontsize=15, fontweight='bold')
ax1.set_ylabel('Price', fontsize=15, fontweight='bold')
ax1.set_title(Coin + ' Price Chart with Buy/Sell Signals', fontsize=18, fontweight='bold', pad=20)

# Plot price and moving averages
ax1.plot(test_022_plot.index, test_022_plot['CLOSE'], label='Price', color='black', alpha=0.70, linewidth=1)
ax1.plot(test_022_plot.index, test_022_plot['P_S_0'], label='P_S_0', color='orange', alpha=0.70, linewidth=1.5)
ax1.plot(test_022_plot.index, test_022_plot['P_S_1'], label='P_S_1', color='red', alpha=0.70, linewidth=1.5)

# Plot buy/sell signals
buy1 = ax1.scatter(test_022_plot.index, test_022_plot['Buy_S_P_018'], color='green', marker='^', s=200, alpha=0.8, zorder=5)
sell1 = ax1.scatter(test_022_plot.index, test_022_plot['Sell_S_P_018'], color='blue', marker='v', s=200, alpha=0.8, zorder=5)

#buy2 = ax1.scatter(test_022_plot.index, test_022_plot['Buy_S_P_016'], color='brown', marker='d', s=200, alpha=0.8, zorder=5)
#sell2 = ax1.scatter(test_022_plot.index, test_022_plot['Sell_S_P_016'], color='brown', marker='d', s=200, alpha=0.8, zorder=5)

#buy2 = ax1.scatter(test_022_plot.index, test_022_plot['Buy_S_P_017'], color='black', marker='d', s=200, alpha=0.8, zorder=5)
#sell2 = ax1.scatter(test_022_plot.index, test_022_plot['Sell_S_P_017'], color='black', marker='d', s=200, alpha=0.8, zorder=5)

# Legend handles
handles = [
    mlines.Line2D([], [], color='black', label='Price', linewidth=1),
    mlines.Line2D([], [], color='orange', label='P_S_0', linewidth=1.5),
    mlines.Line2D([], [], color='red', label='P_S_1', linewidth=1.5),
    mlines.Line2D([], [], marker='^', color='w', label='Buy Signal', markerfacecolor='green', markersize=12),
    mlines.Line2D([], [], marker='v', color='w', label='Sell Signal', markerfacecolor='blue', markersize=12),
    mlines.Line2D([], [], marker='d', color='w', label='Buy/Sell Stop', markerfacecolor='brown', markersize=12)
]

ax1.legend(handles=handles, loc='upper left', frameon=True, fancybox=True, shadow=True, fontsize=12)

# Top label
ax1.text(0.5, 0.95, '@CryptoRobot111', transform=ax1.transAxes, fontsize=12, ha='center', color='cornflowerblue')
ax1.grid(True, alpha=0.5, linestyle='--')

# --------------------
# MACD & RSI chart
# --------------------
ax2.set_title(Coin + ' MACD and RSI Chart', fontsize=18)

ax2.plot(test_022_plot.index, test_022_plot['MACD'], label='MACD Line', color='blue', alpha=0.8, linewidth=1)
ax2.plot(test_022_plot.index, test_022_plot['MACD_signal'], label='MACD Signal', color='red', alpha=0.8, linewidth=1)
ax2.bar(test_022_plot.index, test_022_plot['MACD_histogram'], label='MACD Histogram', color='gray', alpha=0.6, width=0.8)

# RSI on secondary y-axis
ax2_twin = ax2.twinx()
ax2_twin.plot(test_022_plot.index, test_022_plot['RSI'], label='RSI', color='purple', alpha=0.7)
ax2_twin.axhline(y=70, color='red', linestyle='--', alpha=0.5, label='RSI Overbought')
ax2_twin.axhline(y=30, color='green', linestyle='--', alpha=0.5, label='RSI Oversold')
ax2_twin.set_ylabel('RSI', fontsize=12)
ax2_twin.set_ylim(0, 100)

# Legends outside
ax2.legend(loc='upper left', fontsize=12)
ax2_twin.legend(loc='upper right', fontsize=12)

# Rotate x-axis labels for readability
plt.setp(ax2.get_xticklabels(), rotation=45, ha='right', fontsize=12)
plt.setp(ax1.get_xticklabels(), rotation=45, ha='right', fontsize=12)

# Auto-format date labels
fig.autofmt_xdate()

# Save figure
plt.savefig(Coin + '_professional_visible.png', bbox_inches='tight', dpi=150)
plt.show()
